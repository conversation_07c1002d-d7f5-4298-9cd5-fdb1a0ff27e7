export interface StoryViewer {
  id: number;
  name: string;
  profilePhoto: string;
  viewedAt: string; // ISO date string
}

export interface Story {
  id: number;
  userId: number;
  userName: string;
  userProfilePhoto: string;
  mediaUrl: string;
  mediaType: 'image' | 'video';
  caption?: string;
  createdAt: string; // ISO date string
  expiresAt: string; // ISO date string
  viewers: StoryViewer[];
  textColor: string;
}

export interface UserWithStory {
  userId: number;
  hasActiveStory: boolean;
  latestStoryCreatedAt: string; // ISO date string
}

// New types to match API response structure
export interface StoryUpload {
  id: string;
  createdAt: string;
  updatedAt: string;
  active: boolean;
  version: number;
  createdBy: string | null;
  updatedBy: string | null;
  deletedBy: string | null;
  publicId: string;
  secureUrl: string;
  url: string;
  resourceType: 'image' | 'video';
  format: string;
  cloudinaryCreatedAt: string;
  assetId: string;
  type: string;
  displayName: string;
  cloudinaryVersion: string;
  originalFilename: string;
  width: string;
  height: string;
  uploadType: string;
}

export interface StoryUser {
  id: string;
  fullName: string;
  phoneNumber: string;
  email: string;
  username: string;
  profilePicture: StoryUpload[];
  bio: string;
}

export interface UserStoryData {
  id: string;
  createdAt: string;
  updatedAt: string;
  active: boolean;
  version: number;
  user: StoryUser | null;
  storyImages: StoryUpload[];
  expiryDateTime: string;
  // Event-specific properties
  title?: string;
  description?: string;
  location?: string;
  coverImages?: StoryUpload[];
  eventCategory?: {
    id: string;
    categoryName: string;
  };
}
