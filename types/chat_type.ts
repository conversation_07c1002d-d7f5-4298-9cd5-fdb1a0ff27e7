export interface ChatItem {
  id: string; // Changed to string to match Firebase user IDs
  name: string;
  avatar: string;
  lastMessage?: string;
  timestamp: string;
  unread?: number;
  memberCount?: number;
  attendeeCount?: number;
  date?: string;
  chatId?: string; // Firebase chat ID
  groupType?: 'COMMUNITY' | 'EVENT'; // For group chats
  eventId?: string; // For event chats
  communityId?: string; // For community chats
  groupCreatedBy?: string; // Group creator ID
}

export interface Message {
  _id: string; // Changed to string for Firebase compatibility
  text: string;
  createdAt: Date;
  user: {
    _id: string; // Changed to string to match Firebase user IDs
    name: string;
    avatar: string;
  };
  image?: string;
  video?: string;
}

export interface Friend {
  id: string; // Changed to string to match Firebase user IDs
  name: string;
  avatar: string;
  status: 'online' | 'offline';
  email?: string;
  fullName?: string;
}

// Firebase-specific interfaces
export interface FirebaseChatUser {
  id: string;
  name: string;
  avatar: string;
  email: string;
  fullName: string;
}

// Group Chat interfaces
export interface GroupChatItem extends ChatItem {
  groupType: 'COMMUNITY' | 'EVENT';
  eventId?: string;
  communityId?: string;
  groupCreatedBy: string;
  members: { [userId: string]: string | boolean };
}

export interface GroupMessage {
  _id: string;
  text: string;
  createdAt: Date;
  user: {
    _id: string;
    name: string;
    avatar: string;
  };
  image?: string;
  video?: string;
  groupId: string;
}

export interface GroupMember {
  id: string;
  name: string;
  avatar: string;
  email: string;
  fullName: string;
  isAdmin?: boolean;
  joinedAt?: number;
}

// Firebase Group Chat interfaces
export interface FirebaseGroupMessage {
  messageText: string;
  messageTime: number;
  senderId: string;
  uploadUrl?: string;
}

export interface FirebaseGroupChat {
  groupName: string;
  groupIcon: string;
  groupType: 'COMMUNITY' | 'EVENT';
  groupCreatedBy: string;
  communityId?: string;
  eventId?: string;
  members: { [userId: string]: string | boolean };
  messages: { [messageId: string]: FirebaseGroupMessage };
}
