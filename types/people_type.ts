export interface Person {
  id: string | number; // Support both string and number IDs for API compatibility
  long: number;
  lat: number;
  name: string;
  age: number;
  email: string;
  phoneNumber: string;
  bio: string;
  profilePhoto: string;
  profileUploads?: {
    id: string;
    secureUrl: string;
    publicId?: string;
  }[];
  interests: string[];
  isOnline: boolean;
  lastActive: string; // ISO date string
  upFor?: string;
  location?: string;
  visibility?: 'PUBLIC' | 'PRIVATE'; // Add visibility field for privacy filtering
  fullName?: string; // Add fullName for internal processing
}
