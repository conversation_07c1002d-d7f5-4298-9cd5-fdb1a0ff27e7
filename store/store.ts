import { create } from 'zustand';

export const useEventStore = create((set) => ({
  eventData: null,
  setEventData: (data) => set({ eventData: data }),
}));

export const AuthStore = create((set) => ({
  isLoggedIn: false,
  permissions: false,
  initialRegistration: null,
  token: null,
  setToken: (data) => set({ token: data }),
  login: () => set({ isLoggedIn: true }),
  logout: () => set({ isLoggedIn: false }),

  enable: () => set({ permissions: true }),
  disable: () => set({ permissions: false }),

  InitialRegistration: (data) => set({ initialRegistration: data }),
}));

export const UserStore = create((set) => ({
  user: null,
  setUser: (data) => set({ user: data }),
}));
