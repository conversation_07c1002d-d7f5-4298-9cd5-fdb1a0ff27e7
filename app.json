{
  "expo": {
    "name": "Ripple",
    "slug": "ripple-dev",
    "version": "1.0.6",
    "scheme": "ripple",
    "owner": "nojvatech",
    "githubUrl": "https://github.com/NavjoTech/RippleMobile",
    "web": {
      "bundler": "metro",
      "output": "static",
      "favicon": "./assets/favicon.png"
    },
    "plugins": [
      "@react-native-firebase/app",
      [
        "expo-image-picker",
        {
          "photosPermission": "The app accesses your photos to let you share them with your friends.",
          "cameraPermission": "The app accesses your camera to let you capture photos and videos.",
          "microphonePermission": "The app accesses your microphone to let you record videos for stories."
        }
      ],
      [
        "expo-video",
        {
          "supportsBackgroundPlayback": true,
          "supportsPictureInPicture": true
        }
      ],
      [
        "react-native-edge-to-edge",
        {
          "android": {
            "parentTheme": "Default",
            "enforceNavigationBarContrast": false
          }
        }
      ],
      [
        "expo-build-properties",
        {
          "android": {
            "usesCleartextTraffic": true
          },
          "ios": {
            "useFrameworks": "static"
          }
        }
      ],
      "expo-router",
      [
        "@rnmapbox/maps",
        {
          "RNMapboxMapsDownloadToken": "******************************************************************************************",
          "RNMapboxMapsVersion": "11.8.0",
          "androidPermissionsWhenInUse": [
            "android.permission.ACCESS_FINE_LOCATION",
            "android.permission.ACCESS_COARSE_LOCATION"
          ]
        }
      ],
      [
        "expo-location",
        {
          "locationWhenInUsePermission": "Show current location on map."
        }
      ],
      [
        "expo-splash-screen",
        {
          "backgroundColor": "#ffffff",
          "image": "./assets/light_theme_logo1.jpg",
          "dark": {
            "image": "./assets/dark_theme_logo.png",
            "backgroundColor": "#131313"
          },
          "imageWidth": 200
        }
      ],
      "expo-font",
      "expo-secure-store",
      "expo-web-browser",
      "expo-video"
    ],
    "experiments": {
      "typedRoutes": true,
      "tsconfigPaths": true
    },
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "automatic",
    "assetBundlePatterns": [
      "**/*"
    ],
    "ios": {
      "supportsTablet": true,
      "googleServicesFile": "./GoogleService-Info.plist",
      "bundleIdentifier": "com.nojvatech.ripple",
      "infoPlist": {
        "NSAppTransportSecurity": {
          "NSAllowsArbitraryLoads": true
        },
      }
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#ffffff"
      },
      "googleServicesFile": "./google-services.json",
      "package": "com.nojvatech.ripple",
      "edgeToEdgeEnabled": true,
      "versionCode": 5
    },
    "extra": {
      "router": {
        "origin": false
      },
      "eas": {
        "projectId": "d60bc31d-d8b9-466c-bde0-d3561f958310"
      }
    }
  }
}