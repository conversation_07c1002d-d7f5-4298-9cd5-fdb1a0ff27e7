# Social Events API Documentation (Updated)

This document outlines the updated API endpoints for the Social Events application based on the implemented data types and features.

## Table of Contents

1. [Authentication](#authentication)
2. [User Management](#user-management)
3. [Events](#events)
4. [Maps & Location](#maps--location)
5. [Chat & Messaging](#chat--messaging)
6. [Notifications](#notifications)

---

## Authentication

### POST /api/auth/register

Register a new user account.

**Request Body:**

```json
{
  "fullName": "Panashe Mushinyi",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "termsAccepted": true
}
```

**Response (201 Created):**

```json
{
  "success": true,
  "message": "Registration successful. Verification code sent to email.",
  "userId": "user-uuid-123"
}
```

### POST /api/auth/verify-email

Verify user's email with verification code.

**Request Body:**

```json
{
  "userId": "user-uuid-123",
  "verificationCode": "123456"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Email verified successfully",
  "nextStep": "profile-setup"
}
```

### POST /api/auth/login

Authenticate a user and get access token.

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "accessToken": "jwt-token-here",
  "refreshToken": "refresh-token-here",
  "user": {
    "id": "user-uuid-123",
    "fullName": "Panashe Mushinyi",
    "email": "<EMAIL>",
    "isSetupComplete": true,
    "hasPermissions": true,
    "profilePhoto": "https://example.com/profile-image.jpg"
  }
}
```

### POST /api/auth/logout

Invalidate the current session.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

---

## User Management

### POST /api/users/profile-setup

Complete user profile setup after registration.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "username": "panashe_m",
  "gender": "male",
  "age": 28,
  "bio": "Mobile developer passionate about creating seamless user experiences!",
  "interests": ["Technology", "Music", "Art", "Photography", "Travel"],
  "eventPreferences": ["Educational", "Business", "Entertainment", "Leisure"]
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Profile setup completed",
  "user": {
    "id": "user-uuid-123",
    "username": "panashe_m",
    "isSetupComplete": true
  }
}
```

### GET /api/users/profile

Get the current user's profile.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "id": "user-uuid-123",
  "fullName": "Panashe Mushinyi",
  "username": "panashe_m",
  "email": "<EMAIL>",
  "bio": "Mobile developer passionate about creating seamless user experiences!",
  "profilePhoto": "https://example.com/profile-image.jpg",
  "age": 28,
  "location": {
    "lat": -17.8127,
    "long": 31.0701
  },
  "stats": {
    "events": 15,
    "followers": 243,
    "following": 158,
    "attended": 27
  },
  "interests": ["Technology", "Music", "Art", "Photography", "Travel"],
  "isOnline": true,
  "lastActive": "2023-04-01T10:30:00.000Z"
}
```

### PUT /api/users/profile

Update the current user's profile.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "bio": "Updated bio information",
  "interests": ["Technology", "Music", "Photography"],
  "age": 29
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Profile updated successfully"
}
```

---

## Events

### GET /api/events

Get a list of events based on filters.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Query Parameters:**

```
eventType: string (optional)
search: string (optional)
startDate: ISO date string (optional)
endDate: ISO date string (optional)
isPaid: boolean (optional)
visibility: string (optional) - "Public" or "Private"
lat: number (optional)
long: number (optional)
page: number (optional, default: 1)
limit: number (optional, default: 20)
```

**Response (200 OK):**

```json
{
  "events": [
    {
      "id": 11,
      "title": "Startup Pitch Day",
      "description": "Entrepreneurs present their business ideas to potential investors.",
      "location": "Harare Business Hub",
      "locationData": {
        "coordinates": [31.0701, -17.8127],
        "name": "Harare Business Hub",
        "address": "Harare Business Hub, Harare, Zimbabwe"
      },
      "coverImage": null,
      "startDateTime": "2025-04-08T10:30:00.000Z",
      "endDateTime": "2025-04-08T14:00:00.000Z",
      "eventType": "Business",
      "visibility": "Public",
      "isPaid": false,
      "ticketSetup": {
        "hasLevels": false,
        "totalTickets": 100,
        "price": 0,
        "levels": []
      },
      "owners": ["Harare Startup Community"],
      "currency": "USD"
    }
  ],
  "pagination": {
    "total": 42,
    "page": 1,
    "limit": 20,
    "pages": 3
  },
  "eventTypes": ["Business", "Entertainment", "Educational", "Leisure"]
}
```

### GET /api/events/:eventId

Get detailed information about a specific event.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "id": 16,
  "title": "International Business Conference",
  "description": "Global business leaders share insights on market trends and opportunities.",
  "location": "Miekles Hotel",
  "locationData": {
    "coordinates": [31.0729, -17.8234],
    "name": "Miekles Hotel",
    "address": "Miekles Hotel, Harare, Zimbabwe"
  },
  "coverImage": null,
  "startDateTime": "2025-04-13T10:00:00.000Z",
  "endDateTime": "2025-04-13T17:00:00.000Z",
  "eventType": "Business",
  "visibility": "Public",
  "isPaid": true,
  "ticketSetup": {
    "hasLevels": true,
    "totalTickets": null,
    "price": null,
    "levels": [
      { "type": "Standard", "quantity": 100, "price": 100.0 },
      { "type": "Executive", "quantity": 50, "price": 200.0 }
    ]
  },
  "owners": ["Global Business Forum"],
  "currency": "USD",
  "isAttending": false,
  "isSaved": true
}
```

### POST /api/events

Create a new event.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "title": "Tech Meetup 2025",
  "description": "Monthly tech meetup for developers and tech enthusiasts",
  "location": "Innovation Hub, Harare",
  "locationData": {
    "coordinates": [31.0701, -17.8127],
    "name": "Innovation Hub",
    "address": "45 Tech Street, Harare"
  },
  "coverImage": "base64-encoded-image-or-url",
  "startDateTime": "2025-05-20T18:00:00.000Z",
  "endDateTime": "2025-05-20T21:00:00.000Z",
  "eventType": "Educational",
  "visibility": "Public",
  "isPaid": true,
  "ticketSetup": {
    "hasLevels": true,
    "totalTickets": null,
    "price": null,
    "levels": [
      {
        "type": "Basic",
        "quantity": 50,
        "price": 5
      },
      {
        "type": "VIP",
        "quantity": 10,
        "price": 15
      }
    ]
  },
  "currency": "USD"
}
```

**Response (201 Created):**

```json
{
  "success": true,
  "message": "Event created successfully",
  "eventId": 23
}
```

### PUT /api/events/:eventId

Update an existing event.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "title": "Updated Tech Meetup 2025",
  "description": "Updated description",
  "startDateTime": "2025-05-21T18:00:00.000Z",
  "endDateTime": "2025-05-21T21:00:00.000Z"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Event updated successfully"
}
```

### POST /api/events/:eventId/attend

Register to attend an event.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "ticketType": "Basic" // Optional, only for paid events with ticket levels
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Successfully registered for event",
  "ticketId": "ticket-123"
}
```

### DELETE /api/events/:eventId/attend

Cancel attendance to an event.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Event attendance cancelled"
}
```

---

## Maps & Location

### GET /api/maps/events

Get events for map display.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Query Parameters:**

```
lat: number (required)
long: number (required)
radius: number in km (optional, default: 10)
eventType: string (optional)
```

**Response (200 OK):**

```json
{
  "events": [
    {
      "id": 11,
      "title": "Startup Pitch Day",
      "eventType": "Business",
      "lat": -17.8127,
      "long": 31.0701,
      "startDateTime": "2025-04-08T10:30:00.000Z",
      "endDateTime": "2025-04-08T14:00:00.000Z",
      "isPaid": false,
      "location": "Harare Business Hub"
    }
  ]
}
```

### GET /api/maps/people

Get nearby people for map display.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Query Parameters:**

```
lat: number (required)
long: number (required)
radius: number in km (optional, default: 5)
```

**Response (200 OK):**

```json
{
  "people": [
    {
      "id": 1,
      "name": "John Smith",
      "lat": -17.8125,
      "long": 31.0703,
      "profilePhoto": "https://example.com/profile-1.jpg",
      "isOnline": true,
      "age": 30,
      "bio": "Tech enthusiast and adventure seeker",
      "interests": ["Technology", "Travel", "Photography"],
      "lastActive": "2023-04-01T10:30:00.000Z"
    }
  ]
}
```

### POST /api/maps/location

Update user's current location.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "lat": -17.8127,
  "long": 31.0701,
  "shareLocation": true
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Location updated successfully"
}
```

---

## Chat & Messaging

### GET /api/chat/conversations

Get user's conversations.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "conversations": [
    {
      "id": 1,
      "name": "John Smith",
      "avatar": "https://example.com/avatar-1.jpg",
      "lastMessage": "See you at the event tomorrow!",
      "timestamp": "2023-04-01T10:30:00.000Z",
      "unread": 2
    },
    {
      "id": 2,
      "name": "Tech Meetup Group",
      "avatar": "https://example.com/group-avatar.jpg",
      "lastMessage": "Don't forget to bring your laptops",
      "timestamp": "2023-04-01T09:45:00.000Z",
      "unread": 0,
      "memberCount": 15
    }
  ]
}
```

### GET /api/chat/conversations/:conversationId/messages

Get messages for a specific conversation.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Query Parameters:**

```
limit: number (optional, default: 20)
before: string (timestamp, optional)
```

**Response (200 OK):**

```json
{
  "messages": [
    {
      "_id": 1,
      "text": "Hey, are you coming to the event tomorrow?",
      "createdAt": "2023-04-01T10:15:00.000Z",
      "user": {
        "_id": 2,
        "name": "John Smith",
        "avatar": "https://example.com/avatar-1.jpg"
      }
    },
    {
      "_id": 2,
      "text": "Yes, I'll be there!",
      "createdAt": "2023-04-01T10:20:00.000Z",
      "user": {
        "_id": 1,
        "name": "You",
        "avatar": "https://example.com/your-avatar.jpg"
      }
    },
    {
      "_id": 3,
      "text": "See you at the event tomorrow!",
      "createdAt": "2023-04-01T10:30:00.000Z",
      "user": {
        "_id": 2,
        "name": "John Smith",
        "avatar": "https://example.com/avatar-1.jpg"
      }
    }
  ],
  "hasMore": false
}
```

### POST /api/chat/conversations/:conversationId/messages

Send a message in a conversation.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "text": "Looking forward to seeing you!",
  "image": "base64-encoded-image" // Optional
}
```

**Response (201 Created):**

```json
{
  "success": true,
  "message": {
    "_id": 4,
    "text": "Looking forward to seeing you!",
    "createdAt": "2023-04-01T11:00:00.000Z",
    "user": {
      "_id": 1,
      "name": "You",
      "avatar": "https://example.com/your-avatar.jpg"
    }
  }
}
```

### GET /api/chat/friends

Get user's friends list.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "friends": [
    {
      "id": 1,
      "name": "John Smith",
      "avatar": "https://example.com/avatar-1.jpg",
      "status": "online"
    },
    {
      "id": 2,
      "name": "Jane Doe",
      "avatar": "https://example.com/avatar-2.jpg",
      "status": "offline"
    }
  ]
}
```

---

## Notifications

### GET /api/notifications

Get user notifications.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Query Parameters:**

```
page: number (optional, default: 1)
limit: number (optional, default: 20)
```

**Response (200 OK):**

```json
{
  "notifications": [
    {
      "id": 1,
      "type": "friend_request",
      "title": "New Friend Request",
      "message": "John Smith sent you a friend request",
      "data": {
        "userId": 1,
        "requestId": 101
      },
      "isRead": false,
      "createdAt": "2023-04-01T10:30:00.000Z"
    },
    {
      "id": 2,
      "type": "event_reminder",
      "title": "Event Tomorrow",
      "message": "Reminder: Startup Pitch Day is tomorrow at 10:30 AM",
      "data": {
        "eventId": 11
      },
      "isRead": true,
      "createdAt": "2023-04-07T09:00:00.000Z"
    }
  ],
  "unreadCount": 1,
  "pagination": {
    "total": 42,
    "page": 1,
    "limit": 20,
    "pages": 3
  }
}
```

### PUT /api/notifications/:notificationId/read

Mark a notification as read.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Notification marked as read"
}
```

### PUT /api/notifications/read-all

Mark all notifications as read.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "All notifications marked as read"
}
```
