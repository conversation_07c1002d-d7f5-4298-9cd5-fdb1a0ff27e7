import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter, Stack } from 'expo-router';
import { MaterialIcons, FontAwesome5, Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useColorScheme } from '~/lib/useColorScheme';

// Define package types
type PackageFeature = {
  icon: string;
  text: string;
};

type PromotionPackage = {
  id: string;
  name: string;
  price: string;
  duration: string;
  popular?: boolean;
  features: PackageFeature[];
};

export default function PromoteEventScreen() {
  const router = useRouter();
  const { colors, isDark } = useColorScheme();
  const insets = useSafeAreaInsets();
  const [selectedPackage, setSelectedPackage] = useState<string>('premium');

  // Promotion packages data
  const packages: PromotionPackage[] = [
    {
      id: 'basic',
      name: 'Basic Package',
      price: '$9.99',
      duration: '48 hours',
      features: [
        { icon: 'map', text: 'Enhanced map visibility for 48 hours' },
        { icon: 'videocam', text: 'Upload up to 3 promotional videos' },
        { icon: 'image', text: 'Upload up to 5 promotional photos' },
        { icon: 'notifications', text: 'Push notifications to users within 5km radius' },
        { icon: 'star', text: 'Highlight effect on event listing' },
        { icon: 'bookmark', text: 'Featured in "Featured Events" for 24 hours' },
      ],
    },
    {
      id: 'premium',
      name: 'Premium Package',
      price: '$24.99',
      duration: '14 days',
      popular: true,
      features: [
        { icon: 'map', text: 'Enhanced map visibility for 14 days' },
        { icon: 'videocam', text: 'Upload up to 10 promotional videos' },
        { icon: 'image', text: 'Upload up to 15 promotional photos' },
        { icon: 'notifications', text: 'Push notifications to users within 15km radius' },
        { icon: 'play-circle', text: 'Event appears in user feeds as stories for 7 days' },
        { icon: 'time', text: 'Automated countdown notifications to interested users' },
        { icon: 'pricetag', text: 'Offer exclusive discount codes through the app' },
      ],
    },
    {
      id: 'enterprise',
      name: 'Enterprise Package',
      price: '$49.99',
      duration: 'Until event ends',
      features: [
        { icon: 'map', text: 'Permanent enhanced visibility until event concludes' },
        { icon: 'videocam', text: 'Unlimited promotional videos' },
        { icon: 'image', text: 'Unlimited promotional photos' },
        { icon: 'globe', text: 'Global reach with relevance filtering' },
        { icon: 'trophy', text: 'Guaranteed top placement in search results' },
        { icon: 'play-circle', text: 'Appears in all users' },
        { icon: 'pricetag', text: 'Offer exclusive discount codes through the app' },
      ],
    },
  ];

  const handleSelectPackage = (packageId: string) => {
    setSelectedPackage(packageId);
  };

  const handleStartPromotion = () => {
    // In a real app, this would process payment and activate the promotion
    // For now, just navigate back to the event page
    router.push('/Events/eventCreated');
  };

  return (
    <View
      className="flex-1"
      style={{
        backgroundColor: colors.background,
        paddingTop: Platform.OS === 'ios' ? 0 : insets.top,
      }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 40 }}>
        {/* Header with back button */}
        <View className="flex-row items-center justify-between px-4 pt-12">
          <TouchableOpacity onPress={() => router.back()} className="p-2">
            <MaterialIcons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity>

          <Text className="text-xl font-bold" style={{ color: colors.foreground }}>
            Promote Your Event
          </Text>

          <View style={{ width: 28 }} />
        </View>

        {/* Title section */}
        <View className="px-6 pt-6">
          <Text className="text-3xl font-bold" style={{ color: colors.foreground }}>
            Choose A Package
          </Text>
        </View>

        {/* Package selection */}
        <View className="px-6 pt-8">
          {packages.map((pkg) => (
            <TouchableOpacity
              key={pkg.id}
              className={`mb-4 overflow-hidden rounded-xl border-2 ${
                selectedPackage === pkg.id
                  ? 'border-violet-600'
                  : isDark
                    ? 'border-gray-700'
                    : 'border-gray-200'
              } ${isDark ? 'bg-gray-800' : 'bg-white'}`}
              onPress={() => handleSelectPackage(pkg.id)}>
              <View className="flex-row items-center justify-between p-4">
                <View className="flex-1">
                  <Text className="text-lg font-bold" style={{ color: colors.foreground }}>
                    {pkg.name}
                  </Text>
                  {/* <Text className="text-sm" style={{ color: colors.grey }}>
                    {pkg.duration} free then {pkg.price}
                  </Text> */}
                </View>

                <View
                  className={`h-6 w-6 items-center justify-center rounded-full ${
                    selectedPackage === pkg.id
                      ? 'bg-violet-600'
                      : isDark
                        ? 'border border-gray-600'
                        : 'border border-gray-300'
                  }`}>
                  {selectedPackage === pkg.id && (
                    <MaterialIcons name="check" size={16} color="#fff" />
                  )}
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Selected package features */}
        <View className="px-6 mt-4">
          <Text className="mb-4 text-xl font-bold" style={{ color: colors.foreground }}>
            {packages.find((p) => p.id === selectedPackage)?.name} includes:
          </Text>

          {packages
            .find((p) => p.id === selectedPackage)
            ?.features.map((feature, index) => (
              <View key={index} className="flex-row mb-4">
                <View className="mt-1 mr-3">
                  <Ionicons name="checkmark" size={18} color={isDark ? '#a78bfa' : '#7c3aed'} />
                </View>
                <View className="flex-1">
                  <Text className="text-base" style={{ color: colors.foreground }}>
                    {feature.text}
                  </Text>
                </View>
              </View>
            ))}
        </View>

        {/* Start promotion button */}
        <View className="px-6 mt-8">
          <TouchableOpacity
            className="w-full py-4 bg-red-500 rounded-full"
            onPress={handleStartPromotion}>
            <Text className="text-lg font-bold text-center text-white">Start Promotion</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}
