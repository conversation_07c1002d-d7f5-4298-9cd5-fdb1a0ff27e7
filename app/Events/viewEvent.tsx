import { Ionicons, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useRef, useState } from 'react';
import { Text, View, Image, TouchableOpacity, ScrollView, Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Toast } from 'toastify-react-native';

import LocationPreview from '~/components/Map/LocationPreview';
import PersonProfileSheet, {
  PersonProfileSheetHandle,
} from '~/components/People/PersonProfileSheet';

import { useColorScheme } from '~/lib/useColorScheme';
import { getEventStatus } from '~/lib/eventStatusUtils';
import { EventService } from '~/services/EventService';
import { UserStore } from '~/store/store';
import { EventType } from '~/types';
import events from '~/data/events.json';
import PhotoViewModal from '~/components/Profile/PhotoViewModal';

// Ticket Info Component
const TicketsContent: React.FC<{
  event: EventType;
  onBack: () => void;
}> = ({ event, onBack }) => {
  const { colors } = useColorScheme();

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return format(date, 'EEEE, MMMM d');
  };

  const formatTime = (dateString: string | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return format(date, 'h:mm a');
  };

  const getTicketInfo = () => {
    if (!event) return null;

    if (!event.isPaid) {
      return {
        isFree: true,
        price: 'Free',
        totalTickets: event.ticketSetup.totalTickets,
      };
    }

    if (event.ticketSetup.hasLevels) {
      return {
        isFree: false,
        hasLevels: true,
        levels: event.ticketSetup.levels,
      };
    }

    return {
      isFree: false,
      hasLevels: false,
      price: event.ticketSetup.price,
      totalTickets: event.ticketSetup.totalTickets,
    };
  };

  const ticketInfo = getTicketInfo() || { isFree: true, price: 0, totalTickets: 0 };

  return (
    <ScrollView className="flex-1" contentContainerStyle={{ paddingBottom: 20 }}>
      <View className="p-4">
        {/* Back Button */}
        <TouchableOpacity className="mb-4 flex-row items-center" onPress={onBack}>
          <MaterialIcons name="arrow-back" size={24} color={colors.foreground} />
          <Text className="ml-2 font-medium text-base " style={{ color: colors.foreground }}>
            Back to Event
          </Text>
        </TouchableOpacity>

        <View className="mb-6">
          <Text className="light:text-light-text mb-1 font-bold text-2xl dark:text-dark-text">
            {event?.title}
          </Text>
          <Text className="light:text-light-text/70 font-medium text-base dark:text-dark-text/70">
            {formatDate(event?.startDateTime)} • {formatTime(event?.startDateTime)} to{' '}
            {formatTime(event?.endDateTime)}
          </Text>
        </View>

        <View className="mb-6">
          <Text className="light:text-light-text mb-3 font-bold text-lg dark:text-dark-text">
            Registration
          </Text>
          <Text className="light:text-light-text mb-4 text-base dark:text-dark-text">
            {ticketInfo?.isFree
              ? 'Hello! To join the event, please register below.'
              : 'Select your ticket type to continue with your purchase.'}
          </Text>

          {ticketInfo?.isFree ? (
            <TouchableOpacity className="rounded-lg bg-light-primary py-3 dark:bg-dark-primary">
              <Text className="text-center font-bold text-base text-white">Register</Text>
            </TouchableOpacity>
          ) : ticketInfo?.hasLevels ? (
            <View className="space-y-3">
              {ticketInfo.levels?.map((level: any, index: number) => (
                <View
                  key={index}
                  className="rounded-lg border border-gray-200 p-4 dark:border-gray-700">
                  <View className="mb-2 flex-row items-center justify-between">
                    <Text className="light:text-light-text font-bold text-base dark:text-dark-text">
                      {level.type}
                    </Text>
                    <Text className="light:text-light-primary font-bold text-base dark:text-dark-primary">
                      {event.currency} {level.price}
                    </Text>
                  </View>
                  <Text className="light:text-light-text/70 mb-3 text-sm dark:text-dark-text/70">
                    {level.quantity} tickets available
                  </Text>
                  <TouchableOpacity className="rounded-lg bg-light-primary py-2 dark:bg-dark-primary">
                    <Text className="text-center font-bold text-white">Select</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          ) : (
            <View className="mb-4 rounded-lg border border-gray-200 p-4 dark:border-gray-700">
              <View className="mb-2 flex-row items-center justify-between">
                <Text className="light:text-light-text font-bold text-base dark:text-dark-text">
                  Standard Ticket
                </Text>
                <Text className="light:text-light-primary font-bold text-base dark:text-dark-primary">
                  {event.currency} {ticketInfo.price}
                </Text>
              </View>
              <Text className="light:text-light-text/70 mb-3 text-sm dark:text-dark-text/70">
                {ticketInfo.totalTickets} tickets available
              </Text>
              <TouchableOpacity className="rounded-lg bg-light-primary py-2 dark:bg-dark-primary">
                <Text className="text-center font-bold text-white">Buy Ticket</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {!ticketInfo?.isFree && (
          <View className="mb-4 flex-row items-start gap-4">
            <MaterialCommunityIcons name="shield" size={24} color={colors.foreground} />
            <View className="flex-1">
              <Text className="light:text-light-text font-medium text-subtitle dark:text-dark-text">
                Buyer Guarantee Protected
              </Text>
              <Text className="font-regular light:text-light-text/70 text-body dark:text-dark-text/70">
                Every ticket is protected. If your event gets canceled, we'll make it right.
              </Text>
            </View>
          </View>
        )}
      </View>
    </ScrollView>
  );
};

export default function ViewEventScreen() {
  const { colors, isDark } = useColorScheme();
  const router = useRouter();
  const params = useLocalSearchParams();
  const eventId = params.eventId as string;
  const personProfileSheetRef = useRef<PersonProfileSheetHandle>(null);
  const [showTickets, setShowTickets] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [savedEvents, setSavedEvents] = useState<string[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<EventType | null>(null);
  const [photoModalVisible, setPhotoModalVisible] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState<any>(null);
  const insets = useSafeAreaInsets();

  // Get current user from store
  const currentUser = UserStore((state: any) => state.user);

  // Fetch event details
  useEffect(() => {
    const fetchEventDetails = async () => {
      if (eventId) {
        try {
          // Use getEvents with eventId as query parameter
          const response = await EventService.getEvents({ eventId });

          if (response.success && response.body) {
            setSelectedEvent(response.body as EventType);
          } else {
            Toast.show({
              type: 'error',
              text1: 'Error',
              text2: 'Event not found',
              position: 'bottom',
            });
            router.back();
          }
        } catch (error) {
          console.error('Error fetching event details:', error);
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Failed to load event details',
            position: 'bottom',
          });
        }
      }
    };

    fetchEventDetails();
  }, [eventId]);

  // Fetch saved events for the user
  useEffect(() => {
    const fetchSavedEvents = async () => {
      if (currentUser?.id) {
        try {
          const response = await EventService.getSavedEvents(currentUser.id);
          if (response.success && response.body) {
            const eventIds = response.body.events.map((event: any) =>
              (event.id || event.eventId || event._id).toString()
            );
            setSavedEvents(eventIds);
          }
        } catch (error) {
          console.error('Error fetching saved events:', error);
        }
      }
    };

    fetchSavedEvents();
  }, [currentUser?.id]);

  // Check if the selected event is saved when selectedEvent or savedEvents change
  useEffect(() => {
    if (selectedEvent && savedEvents.length > 0) {
      const isEventSaved = savedEvents.includes(selectedEvent.id.toString());
      setIsSaved(isEventSaved);
    } else {
      setIsSaved(false);
    }
  }, [selectedEvent, savedEvents]);

  // Handle save/unsave event
  const handleSaveEvent = async () => {
    if (!selectedEvent || !currentUser?.id) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Unable to save event. Please try again.',
        position: 'bottom',
      });
      return;
    }

    setIsSaving(true);
    try {
      await EventService.saveEvent(currentUser.id, selectedEvent.id.toString());

      // Update local saved events state
      if (isSaved) {
        // Remove from saved events
        setSavedEvents((prev) => prev.filter((id) => id !== selectedEvent.id.toString()));
      } else {
        // Add to saved events
        setSavedEvents((prev) => [...prev, selectedEvent.id.toString()]);
      }

      setIsSaved(!isSaved);

      Toast.show({
        type: 'success',
        text1: isSaved ? 'Event Unsaved' : 'Event Saved',
        text2: isSaved
          ? 'Event removed from your saved events'
          : 'Event added to your saved events',
        position: 'bottom',
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to save event. Please try again.',
        position: 'bottom',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const getEventImage = () => {
    // Use a default image if no cover image is provided
    console.log('selected event cover image', selectedEvent);
    if (!selectedEvent?.coverImage) {
      const eventTypeImages: Record<string, string> = {
        Business:
          'https://images.unsplash.com/photo-1565398305935-49e5dcc5c9f6?q=80&w=1920&auto=format',
        Leisure:
          'https://images.unsplash.com/photo-1517457373958-b7bdd4587205?q=80&w=1920&auto=format',
        Entertainment:
          'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?q=80&w=1920&auto=format',
        Educational:
          'https://images.unsplash.com/photo-1524178232363-1fb2b075b655?q=80&w=1920&auto=format',
      };
      const eventType = selectedEvent?.eventType || '';
      return (
        eventTypeImages[eventType] || 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4'
      );
    }
    return selectedEvent.coverImage;
  };

  const handlePhotoPress = (photo: any) => {
    setSelectedPhoto(photo);
    setPhotoModalVisible(true);
  };

  if (!selectedEvent) {
    return (
      <View
        className="flex-1 items-center justify-center"
        style={{ backgroundColor: colors.background }}>
        <StatusBar style={isDark ? 'light' : 'dark'} />
        <Text className="text-lg" style={{ color: colors.foreground }}>
          Loading event details...
        </Text>
      </View>
    );
  }

  return (
    <View
      className="flex-1"
      style={{
        backgroundColor: colors.background,
        paddingTop: Platform.OS === 'ios' ? insets.top : insets.top + 15,
      }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Header with Close Button - Fixed at top */}
      <View
        className="flex-row items-center justify-between px-4 py-4"
        style={{ borderBottomColor: colors.grey5, borderBottomWidth: 1 }}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
          Event Details
        </Text>
        <View style={{ width: 24 }} />
      </View>

      {showTickets ? (
        <TicketsContent event={selectedEvent} onBack={() => setShowTickets(false)} />
      ) : (
        <ScrollView
          className="flex-1"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}>
          {/* Event Header Image */}
          <View className="relative h-64 w-full">
            <Image
              source={{ uri: getEventImage() }}
              className="h-full w-full"
              style={{ resizeMode: 'cover' }}
            />

            {/* Event Status Chip */}
            {(() => {
              const eventStatus = getEventStatus(
                selectedEvent?.startDateTime,
                selectedEvent?.endDateTime
              );

              if (!eventStatus.status) return null;

              return (
                <View
                  className="absolute right-2 top-2 rounded-full px-2 py-1"
                  style={{ backgroundColor: eventStatus.backgroundColor }}>
                  <Text className="font-bold text-xs text-white">{eventStatus.status}</Text>
                </View>
              );
            })()}

            <View className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
              <Text className="mb-1 font-bold text-2xl uppercase text-white">
                {selectedEvent?.title}
              </Text>
              <Text className="text-base text-white">{selectedEvent?.location}</Text>
            </View>
          </View>

          {/* Event Details */}
          <View className="p-4">
            <Text className="light:text-light-text mb-4 font-bold text-2xl dark:text-dark-text">
              {selectedEvent?.location} - {selectedEvent?.title}
            </Text>

            {/* Date and Time */}
            <View className="mb-6 flex-row items-center justify-between">
              <View className="flex-1">
                <Text className="light:text-light-text font-medium text-lg dark:text-dark-text">
                  {selectedEvent?.startDateTime
                    ? format(new Date(selectedEvent.startDateTime), 'EEEE')
                    : ''}
                </Text>

                <Text className="light:text-light-text/70 text-base dark:text-dark-text/70">
                  {selectedEvent?.startDateTime
                    ? format(new Date(selectedEvent.startDateTime), 'MMMM d, h:mm a')
                    : ''}{' '}
                  to{' '}
                  {selectedEvent?.endDateTime
                    ? format(new Date(selectedEvent.endDateTime), 'h:mm a')
                    : ''}
                </Text>
                <Text className="light:text-light-text/70 mb-2 text-base dark:text-dark-text/70">
                  {selectedEvent?.locationData.address || selectedEvent?.location}
                </Text>
              </View>
            </View>

            {/* Location */}
            <View className="mb-6">
              {selectedEvent?.locationData && (
                <LocationPreview
                  location={{
                    coordinates: selectedEvent.locationData.coordinates,
                    manualAddress: selectedEvent.locationData.address,
                    name: selectedEvent.locationData.name,
                    address: selectedEvent.locationData.address || '',
                  }}
                  editable={false}
                />
              )}
            </View>

            {/* Registration Section */}
            <View className="mb-6">
              <View className="flex-row gap-3">
                <TouchableOpacity
                  onPress={() => setShowTickets(true)}
                  className="flex-1 flex-row items-center justify-center rounded-full bg-light-primary px-4 py-3 dark:bg-dark-primary">
                  <MaterialIcons
                    name="how-to-reg"
                    size={20}
                    color="white"
                    style={{ marginRight: 8 }}
                  />
                  <Text className="text-center font-bold text-base text-white">
                    {selectedEvent?.isPaid ? 'View Tickets' : 'Register'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={handleSaveEvent}
                  disabled={isSaving}
                  className="flex-row items-center justify-center rounded-full border-2 px-4 py-3"
                  style={{
                    borderColor: colors.primary,
                    backgroundColor: isSaved ? colors.primary : 'transparent',
                    opacity: isSaving ? 0.7 : 1,
                  }}>
                  <Ionicons
                    name={isSaved ? 'bookmark' : 'bookmark-outline'}
                    size={20}
                    color={isSaved ? 'white' : colors.primary}
                  />
                  <Text
                    className="ml-2 font-bold text-sm"
                    style={{ color: isSaved ? 'white' : colors.primary }}>
                    {isSaving ? 'Saving...' : isSaved ? 'Saved' : 'Save'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* About Event Section */}
            <View className="mb-6">
              <Text className="light:text-light-text mb-2 font-bold text-lg dark:text-dark-text">
                About Event
              </Text>
              <Text className="light:text-light-text text-base dark:text-dark-text">
                {selectedEvent?.description}
              </Text>
            </View>

            {/* Event Uploads Section */}
            {selectedEvent?.eventUploads && selectedEvent.eventUploads.length > 0 && (
              <View className="mb-6">
                <Text className="light:text-light-text mb-3 font-bold text-lg dark:text-dark-text">
                  Event Photos
                </Text>
                <View className="-mx-1 flex-row flex-wrap">
                  {selectedEvent.eventUploads.map((upload: any, index: number) => (
                    <TouchableOpacity
                      key={upload.id || index}
                      className="mb-2 w-1/3 px-1"
                      onPress={() => handlePhotoPress(upload)}>
                      <Image
                        source={{ uri: upload.secureUrl }}
                        className="aspect-square w-full rounded-lg"
                        style={{ resizeMode: 'cover' }}
                      />
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}

            {/* Hosts Section */}
            <View className="mb-6">
              <Text className="light:text-light-text mb-3 font-bold text-lg dark:text-dark-text">
                Host
              </Text>

              <TouchableOpacity
                className="mb-2 flex-row items-center"
                onPress={() => {
                  if (selectedEvent?.user?.id) {
                    personProfileSheetRef.current?.present(selectedEvent.user.id);
                  }
                }}>
                <Image
                  source={{
                    uri:
                      selectedEvent?.user?.profilePicture &&
                      Array.isArray(selectedEvent?.user.profilePicture) &&
                      selectedEvent?.user.profilePicture.length > 0 &&
                      selectedEvent?.user.profilePicture[
                        selectedEvent?.user.profilePicture.length - 1
                      ]?.secureUrl
                        ? selectedEvent?.user.profilePicture[
                            selectedEvent?.user.profilePicture.length - 1
                          ].secureUrl
                        : 'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                  }}
                  className="mr-3 h-10 w-10 rounded-full"
                  style={{ resizeMode: 'cover' }}
                />
                <View className="flex-1">
                  <Text className="light:text-light-text font-medium text-base dark:text-dark-text">
                    {selectedEvent?.user.fullName}
                  </Text>
                  <View className="mt-1 flex-row items-center">
                    {/* Display 5 stars with random rating for demo purposes */}
                    {[1, 2, 3, 4, 5].map((star) => {
                      // Generate a random rating between 3-5 for demo
                      const rating = 3 + Math.floor(Math.random() * 2.1);
                      return (
                        <Ionicons
                          key={star}
                          name={star <= rating ? 'star' : 'star-outline'}
                          size={14}
                          color="#FFD700"
                          style={{ marginRight: 2 }}
                        />
                      );
                    })}
                    <Text className="light:text-light-text/70 ml-1 text-xs dark:text-dark-text/70">
                      {(3 + Math.random() * 2).toFixed(1)}/5.0
                    </Text>
                  </View>
                </View>
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={colors.grey}
                  style={{ marginLeft: 8 }}
                />
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      )}
      <PersonProfileSheet ref={personProfileSheetRef} />
      <PhotoViewModal
        visible={photoModalVisible}
        photo={selectedPhoto}
        onClose={() => setPhotoModalVisible(false)}
      />
    </View>
  );
}
