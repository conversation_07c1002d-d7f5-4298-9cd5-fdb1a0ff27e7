import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  FlatList,
  TextInput,
  Platform,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter, Stack, useLocalSearchParams } from 'expo-router';
import { Ionicons, MaterialIcons, FontAwesome5, MaterialCommunityIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import ImagePicker from 'react-native-image-crop-picker';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';

import { useColorScheme } from '~/lib/useColorScheme';
import { Button, ButtonText } from '~/components/ui/button';
import { RenderBackdrop } from '~/components/RenderBackdrop';
import events from '~/data/events.json';

export default function EventPromotionsScreen() {
  const router = useRouter();
  const { colors, isDark } = useColorScheme();
  const insets = useSafeAreaInsets();
  const params = useLocalSearchParams();
  const eventId = params.eventId ? Number(params.eventId) : null;

  const [event, setEvent] = useState(null);
  const [activeTab, setActiveTab] = useState('gallery'); // 'gallery', 'videos', 'stories'

  // State for gallery, videos, and stories
  const [galleryImages, setGalleryImages] = useState([]);
  const [videos, setVideos] = useState([]);
  const [stories, setStories] = useState([]);

  // State for story creation
  const [storyText, setStoryText] = useState('');
  const [storyBackground, setStoryBackground] = useState(null);

  // Bottom sheet references
  const createStorySheetRef = useRef(null);

  // Bottom sheet snap points
  const storySnapPoints = ['70%'];

  React.useEffect(() => {
    if (eventId) {
      // In a real app, you would fetch event data from an API
      const foundEvent = events.find((e) => e.id === eventId);
      if (foundEvent) {
        setEvent(foundEvent);
      }
    }
  }, [eventId]);

  const pickImage = async (forGallery = true) => {
    try {
      const media = await ImagePicker.openPicker({
        mediaType: forGallery ? 'photo' : 'video',
        cropping: forGallery,
        compressImageQuality: 0.8,
        freeStyleCropEnabled: true,
        includeBase64: false,
        cropperStatusBarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarWidgetColor: Platform.OS === 'android' ? colors.foreground : undefined,
        cropperActiveWidgetColor: Platform.OS === 'android' ? colors.primary : undefined,
        showCropGuidelines: Platform.OS === 'android' ? true : undefined,
        showCropFrame: Platform.OS === 'android' ? true : undefined,
        hideBottomControls: Platform.OS === 'android' ? false : undefined,
        enableRotationGesture: Platform.OS === 'android' ? true : undefined,
        disableCropperColorSetters: Platform.OS === 'android' ? false : undefined,
        // iOS-specific configurations
        cropperChooseColor: Platform.OS === 'ios' ? colors.primary : undefined,
        cropperCancelColor: Platform.OS === 'ios' ? colors.foreground : undefined,
        avoidEmptySpaceAroundImage: Platform.OS === 'ios' ? true : undefined,
        cropperToolbarTitle: 'Edit Photo',
      });

      if (forGallery) {
        if (activeTab === 'gallery') {
          setGalleryImages([...galleryImages, media.path]);
        } else if (activeTab === 'stories') {
          setStoryBackground(media.path);
        }
      } else {
        setVideos([...videos, media.path]);
      }
    } catch (error: any) {
      if (error.code !== 'E_PICKER_CANCELLED') {
        console.error('Error picking media:', error);
      }
    }
  };

  const openCreateStorySheet = () => {
    createStorySheetRef.current?.expand();
  };

  const closeCreateStorySheet = () => {
    createStorySheetRef.current?.close();
  };

  const createStory = () => {
    if (storyText.trim() || storyBackground) {
      const newStory = {
        id: Date.now().toString(),
        text: storyText,
        background: storyBackground,
        timestamp: new Date().toISOString(),
      };

      setStories([...stories, newStory]);
      setStoryText('');
      setStoryBackground(null);
      closeCreateStorySheet();
    }
  };

  const renderGalleryTab = () => (
    <View className="mt-4">
      <View className="flex-row flex-wrap justify-between px-4">
        {galleryImages.map((uri, index) => (
          <View key={index} className="mb-4 h-[110px] w-[30%] overflow-hidden rounded-xl">
            <Image source={{ uri }} className="w-full h-full" resizeMode="cover" />
          </View>
        ))}

        <TouchableOpacity
          className="mb-4 h-[110px] w-[30%] items-center justify-center rounded-xl"
          style={{ backgroundColor: colors.grey5 }}
          onPress={() => pickImage(true)}>
          <Ionicons name="add" size={30} color={colors.grey} />
          <Text className="mt-1 text-xs" style={{ color: colors.grey }}>
            Add Photo
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderVideosTab = () => (
    <View className="mt-4">
      <View className="flex-row flex-wrap justify-between px-4">
        {videos.map((uri, index) => (
          <View key={index} className="mb-4 h-[180px] w-[48%] overflow-hidden rounded-xl">
            <Image source={{ uri }} className="w-full h-full" resizeMode="cover" />
            <View className="absolute items-center justify-center w-8 h-8 rounded-full bottom-2 right-2 bg-black/50">
              <Ionicons name="play" size={16} color="#fff" />
            </View>
          </View>
        ))}

        <TouchableOpacity
          className="mb-4 h-[180px] w-[48%] items-center justify-center rounded-xl"
          style={{ backgroundColor: colors.grey5 }}
          onPress={() => pickImage(false)}>
          <Ionicons name="videocam" size={30} color={colors.grey} />
          <Text className="mt-1 text-xs" style={{ color: colors.grey }}>
            Add Video
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderStoriesTab = () => (
    <View className="mt-4">
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 16 }}>
        {/* Add Story Button */}
        <TouchableOpacity
          className="mr-4 h-[180px] w-[120px] items-center justify-center rounded-xl"
          style={{ backgroundColor: colors.grey5 }}
          onPress={openCreateStorySheet}>
          <Ionicons name="add" size={30} color={colors.grey} />
          <Text className="mt-1 text-xs" style={{ color: colors.grey }}>
            Add Story
          </Text>
        </TouchableOpacity>

        {/* Story Items */}
        {stories.map((story, index) => (
          <View key={index} className="mr-4 h-[180px] w-[120px] overflow-hidden rounded-xl">
            {story.background ? (
              <Image
                source={{ uri: story.background }}
                className="w-full h-full"
                resizeMode="cover"
              />
            ) : (
              <View className="w-full h-full bg-violet-600" />
            )}
            {story.text && (
              <View className="absolute bottom-0 left-0 right-0 p-2 bg-black/50">
                <Text className="text-xs text-white" numberOfLines={2}>
                  {story.text}
                </Text>
              </View>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );

  if (!event) {
    return (
      <View
        className="items-center justify-center flex-1"
        style={{ backgroundColor: colors.background }}>
        <Text style={{ color: colors.foreground }}>Loading event data...</Text>
      </View>
    );
  }

  return (
    <View
      className="flex-1"
      style={{
        backgroundColor: colors.background,
        paddingTop: insets.top,
      }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Header */}
      <View className="flex-row items-center justify-between px-4 py-4">
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text className="text-xl font-bold" style={{ color: colors.foreground }}>
          Event Promotions
        </Text>
        <View style={{ width: 24 }} />
      </View>

      {/* Event Title */}
      <View className="px-4 pb-4">
        <Text className="text-base font-medium" style={{ color: colors.grey }}>
          {event.title}
        </Text>
      </View>

      {/* Tabs */}
      <View className="flex-row px-4 border-b" style={{ borderColor: colors.grey5 }}>
        <TouchableOpacity
          className={`mr-6 pb-3 ${activeTab === 'gallery' ? 'border-b-2' : ''}`}
          style={{ borderColor: activeTab === 'gallery' ? colors.violet : 'transparent' }}
          onPress={() => setActiveTab('gallery')}>
          <Text
            className="font-medium"
            style={{ color: activeTab === 'gallery' ? colors.foreground : colors.grey }}>
            Gallery
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          className={`mr-6 pb-3 ${activeTab === 'videos' ? 'border-b-2' : ''}`}
          style={{ borderColor: activeTab === 'videos' ? colors.violet : 'transparent' }}
          onPress={() => setActiveTab('videos')}>
          <Text
            className="font-medium"
            style={{ color: activeTab === 'videos' ? colors.foreground : colors.grey }}>
            Videos
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          className={`pb-3 ${activeTab === 'stories' ? 'border-b-2' : ''}`}
          style={{ borderColor: activeTab === 'stories' ? colors.violet : 'transparent' }}
          onPress={() => setActiveTab('stories')}>
          <Text
            className="font-medium"
            style={{ color: activeTab === 'stories' ? colors.foreground : colors.grey }}>
            Stories
          </Text>
        </TouchableOpacity>
      </View>

      {/* Tab Content */}
      {activeTab === 'gallery' && renderGalleryTab()}
      {activeTab === 'videos' && renderVideosTab()}
      {activeTab === 'stories' && renderStoriesTab()}

      {/* Create Story Bottom Sheet */}
      <BottomSheet
        ref={createStorySheetRef}
        index={-1}
        snapPoints={storySnapPoints}
        enablePanDownToClose
        backdropComponent={RenderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <BottomSheetView className="flex-1 p-4">
          <Text className="mb-4 text-xl font-bold" style={{ color: colors.foreground }}>
            Create Story
          </Text>

          {/* Story Background Preview */}
          <TouchableOpacity
            className="mb-4 h-[200px] w-full items-center justify-center rounded-xl"
            style={{ backgroundColor: storyBackground ? 'transparent' : colors.grey5 }}
            onPress={() => pickImage(true)}>
            {storyBackground ? (
              <Image
                source={{ uri: storyBackground }}
                className="w-full h-full rounded-xl"
                resizeMode="cover"
              />
            ) : (
              <>
                <Ionicons name="image" size={30} color={colors.grey} />
                <Text className="mt-1 text-sm" style={{ color: colors.grey }}>
                  Add Background Image
                </Text>
              </>
            )}
          </TouchableOpacity>

          {/* Story Text Input */}
          <TextInput
            className="mb-4 h-[100px] rounded-xl p-3 text-base"
            style={{
              backgroundColor: colors.grey5,
              color: colors.foreground,
            }}
            placeholder="Write your story..."
            placeholderTextColor={colors.grey}
            multiline
            value={storyText}
            onChangeText={setStoryText}
          />

          {/* Create Button */}
          <Button
            size="lg"
            variant="solid"
            className={`h-14 rounded-xl ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
            onPress={createStory}>
            <Text className="font-semibold text-white">Create Story</Text>
          </Button>
        </BottomSheetView>
      </BottomSheet>
    </View>
  );
}
