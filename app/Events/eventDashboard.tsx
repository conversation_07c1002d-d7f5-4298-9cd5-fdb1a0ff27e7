import React, { useState, useEffect, useRef } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter, Stack, useLocalSearchParams } from 'expo-router';
import { Ionicons, MaterialIcons, FontAwesome5 } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LineChart } from 'react-native-chart-kit';
import { Toast } from 'toastify-react-native';
import { format } from 'date-fns';

import { useColorScheme } from '~/lib/useColorScheme';
import { Button } from '~/components/ui/button';
import { EventService } from '~/services/EventService';
import { EventType } from '~/types';
import LocationPreview from '~/components/Map/LocationPreview';
import EventPhotoUploadBottomSheet, {
  EventPhotoUploadBottomSheetHandle,
} from '~/components/Events/EventPhotoUploadBottomSheet';
import events from '~/data/events.json';

export default function EventDashboardScreen() {
  const router = useRouter();
  const { colors, isDark } = useColorScheme();
  const insets = useSafeAreaInsets();
  const params = useLocalSearchParams();
  const eventId = params.eventId as string;
  const photoUploadSheetRef = useRef<EventPhotoUploadBottomSheetHandle>(null);

  const [event, setEvent] = useState<EventType | null>(null);
  const [ticketsSold, setTicketsSold] = useState(0);
  const [revenue, setRevenue] = useState(0);
  const [attendees, setAttendees] = useState(0);
  const [eventPhotos, setEventPhotos] = useState<any[]>([]);

  // Mock data for charts
  const salesData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        data: [5, 8, 12, 15, 20, 25, 30],
        color: (opacity = 1) =>
          isDark ? `rgba(139, 92, 246, ${opacity})` : `rgba(124, 58, 237, ${opacity})`,
        strokeWidth: 2,
      },
    ],
  };

  // Fetch event details
  useEffect(() => {
    const fetchEventDetails = async () => {
      if (eventId) {
        try {
          // Use getEvents with eventId as query parameter
          const response = await EventService.getEvents({ eventId });

          if (response.success && response.body) {
            setEvent(response.body as EventType);
            setEventPhotos(response.body.eventUploads || []);

            // Calculate mock statistics
            let totalTickets = 0;
            let totalRevenue = 0;

            if (response.body.ticketSetup?.hasLevels && response.body.ticketSetup?.levels) {
              response.body.ticketSetup.levels.forEach((level: any) => {
                // Simulate that 60% of tickets are sold
                const soldForLevel = Math.floor(level.quantity * 0.6);
                totalTickets += soldForLevel;
                totalRevenue += soldForLevel * level.price;
              });
            } else if (response.body.ticketSetup?.totalTickets) {
              // Simulate that 60% of tickets are sold
              totalTickets = Math.floor(response.body.ticketSetup.totalTickets * 0.6);
              if (response.body.isPaid && response.body.ticketSetup?.price) {
                totalRevenue = totalTickets * response.body.ticketSetup.price;
              }
            }

            setTicketsSold(totalTickets);
            setRevenue(totalRevenue);
            setAttendees(Math.floor(totalTickets * 0.9)); // Assume 90% of ticket holders attended
          } else {
            Toast.show({
              type: 'error',
              text1: 'Error',
              text2: 'Event not found',
              position: 'bottom',
            });
            router.back();
          }
        } catch (error) {
          console.error('Error fetching event details:', error);
          // Fallback to static events data on API error
          const foundEvent = events.find((event: any) => event.id.toString() === eventId);
          if (foundEvent) {
            setEvent(foundEvent as EventType);
          } else {
            Toast.show({
              type: 'error',
              text1: 'Error',
              text2: 'Failed to load event details',
              position: 'bottom',
            });
          }
        }
      }
    };

    fetchEventDetails();
  }, [eventId]);

  const handlePromoteEvent = () => {
    router.push({
      pathname: '/Events/eventPromotions',
      params: { eventId },
    });
  };

  const handleCashOut = () => {
    // In a real app, this would initiate a payment process
    alert(
      'Cash out request initiated. Funds will be transferred to your account within 3-5 business days.'
    );
  };

  const handleAddEventPhoto = () => {
    photoUploadSheetRef.current?.present();
  };

  const handlePhotosUpdate = (photos: any[]) => {
    setEventPhotos(photos);
  };

  const getEventImage = () => {
    if (event?.coverImages) {
      return event.coverImages[0].secureUrl;
    }
    return 'https://images.unsplash.com/photo-*************-f7f57925c3b4';
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return format(date, 'EEEE, MMMM d');
  };

  const formatTime = (dateString: string | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return format(date, 'h:mm a');
  };

  if (!event) {
    return (
      <View
        className="items-center justify-center flex-1"
        style={{ backgroundColor: colors.background }}>
        <Text className="text-lg" style={{ color: colors.foreground }}>
          Loading event details...
        </Text>
      </View>
    );
  }

  return (
    <View
      className="flex-1"
      style={{
        backgroundColor: colors.background,
        paddingTop: insets.top,
      }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Header */}
      <View className="flex-row items-center justify-between px-4 py-4">
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text className="text-xl font-bold" style={{ color: colors.foreground }}>
          Event Dashboard
        </Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 40 }}>
        {/* Event Header Image - Similar to viewEvent */}
        <View className="relative w-full h-64">
          <Image
            source={{ uri: getEventImage() }}
            className="w-full h-full"
            style={{ resizeMode: 'cover' }}
          />
          <View className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent">
            <Text className="mb-1 text-2xl font-bold text-white uppercase">{event?.title}</Text>
            <Text className="text-base text-white">{event?.location}</Text>
          </View>
        </View>

        {/* Event Details Section */}
        <View className="p-4">
          <Text className="mb-4 text-2xl font-bold light:text-light-text dark:text-dark-text">
            {event?.location} - {event?.title}
          </Text>

          {/* Date and Time */}
          <View className="flex-row items-center justify-between mb-6">
            <View className="flex-1">
              <Text className="text-lg font-medium light:text-light-text dark:text-dark-text">
                {event?.startDateTime ? formatDate(event.startDateTime) : ''}
              </Text>
              <Text className="text-base light:text-light-text/70 dark:text-dark-text/70">
                {event?.startDateTime ? formatTime(event.startDateTime) : ''} to{' '}
                {event?.endDateTime ? formatTime(event.endDateTime) : ''}
              </Text>
              <Text className="mb-2 text-base light:text-light-text/70 dark:text-dark-text/70">
                {event?.locationData?.address || event?.location}
              </Text>
            </View>
          </View>

          {/* Location Preview */}
          {event?.locationData && (
            <View className="mb-6">
              <LocationPreview
                location={{
                  coordinates: event.locationData.coordinates,
                  manualAddress: event.locationData.address,
                  name: event.locationData.name,
                  address: event.locationData.address || '',
                }}
                editable={false}
              />
            </View>
          )}

          {/* Add Event Photos Section */}
          <View className="mb-6">
            <View className="flex-row items-center justify-between mb-4">
              <Text className="text-lg font-bold" style={{ color: colors.foreground }}>
                Event Photos
              </Text>
              <TouchableOpacity
                onPress={handleAddEventPhoto}
                className="flex-row items-center px-3 py-2 rounded-lg"
                style={{ backgroundColor: colors.primary }}>
                <Ionicons name="camera" size={16} color="white" style={{ marginRight: 4 }} />
                <Text className="text-sm font-medium text-white">Add Photo</Text>
              </TouchableOpacity>
            </View>

            {eventPhotos.length > 0 ? (
              <ScrollView horizontal showsHorizontalScrollIndicator={false} className="gap-2">
                {eventPhotos.map((photo, index) => (
                  <Image
                    key={index}
                    source={{ uri: photo.secureUrl }}
                    className="w-24 h-24 mr-2 rounded-lg"
                    style={{ resizeMode: 'cover' }}
                  />
                ))}
              </ScrollView>
            ) : (
              <View
                className="items-center justify-center h-24 border-2 border-dashed rounded-lg"
                style={{ borderColor: colors.grey }}>
                <Text className="text-sm" style={{ color: colors.grey }}>
                  No photos added yet
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Stats Cards */}
        <View className="flex-row justify-between mx-4 mb-6">
          {/* Tickets Sold */}
          <View className="w-[31%] rounded-xl p-3" style={{ backgroundColor: colors.grey5 }}>
            <View className="items-center justify-center w-8 h-8 mb-2 rounded-full bg-violet-600/20">
              <FontAwesome5 name="ticket-alt" size={16} color={isDark ? '#d1d5db' : '#6b7280'} />
            </View>
            <Text className="text-xl font-bold" style={{ color: colors.foreground }}>
              {ticketsSold}
            </Text>
            <Text className="text-xs" style={{ color: colors.grey }}>
              Tickets Sold
            </Text>
          </View>

          {/* Revenue */}
          <View className="w-[31%] rounded-xl p-3" style={{ backgroundColor: colors.grey5 }}>
            <View className="items-center justify-center w-8 h-8 mb-2 rounded-full bg-green-600/20">
              <FontAwesome5 name="dollar-sign" size={16} color={isDark ? '#d1d5db' : '#6b7280'} />
            </View>
            <Text className="text-xl font-bold" style={{ color: colors.foreground }}>
              ${revenue}
            </Text>
            <Text className="text-xs" style={{ color: colors.grey }}>
              Revenue
            </Text>
          </View>

          {/* Attendees */}
          <View className="w-[31%] rounded-xl p-3" style={{ backgroundColor: colors.grey5 }}>
            <View className="items-center justify-center w-8 h-8 mb-2 rounded-full bg-blue-600/20">
              <Ionicons name="people" size={16} color={isDark ? '#d1d5db' : '#6b7280'} />
            </View>
            <Text className="text-xl font-bold" style={{ color: colors.foreground }}>
              {attendees}
            </Text>
            <Text className="text-xs" style={{ color: colors.grey }}>
              Attendees
            </Text>
          </View>
        </View>

        {/* Sales Chart */}
        <View className="p-4 mx-4 mb-6 rounded-xl" style={{ backgroundColor: colors.grey5 }}>
          <Text className="mb-4 text-lg font-bold" style={{ color: colors.foreground }}>
            Ticket Sales
          </Text>
          <LineChart
            data={salesData}
            width={320}
            height={180}
            chartConfig={{
              backgroundColor: 'transparent',
              backgroundGradientFrom: 'transparent',
              backgroundGradientTo: 'transparent',
              decimalPlaces: 0,
              color: (opacity = 1) =>
                isDark ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
              labelColor: (opacity = 1) =>
                isDark ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
              style: {
                borderRadius: 16,
              },
              propsForDots: {
                r: '6',
                strokeWidth: '2',
                stroke: isDark ? '#8b5cf6' : '#7c3aed',
              },
            }}
            bezier
            style={{
              marginVertical: 8,
              borderRadius: 16,
            }}
          />
        </View>

        {/* Action Buttons */}
        <View className="mx-4 mb-6 space-y-4">
          {/* Promote Event Button */}
          <TouchableOpacity
            className="flex-row items-center p-4 rounded-xl"
            style={{ backgroundColor: colors.grey5 }}
            onPress={handlePromoteEvent}>
            <View className="items-center justify-center w-10 h-10 mr-4 rounded-full bg-violet-600/20">
              <MaterialIcons name="campaign" size={20} color={isDark ? '#d1d5db' : '#6b7280'} />
            </View>
            <View className="flex-1">
              <Text className="text-base font-bold" style={{ color: colors.foreground }}>
                Promote Event
              </Text>
              <Text className="text-sm" style={{ color: colors.grey }}>
                Add gallery, videos, and stories
              </Text>
            </View>
            <MaterialIcons name="chevron-right" size={24} color={isDark ? '#d1d5db' : '#6b7280'} />
          </TouchableOpacity>

          {/* Cash Out Button */}
          {revenue > 0 && (
            <Button
              size="lg"
              variant="solid"
              className={`h-14 rounded-xl ${isDark ? 'bg-green-700' : 'bg-green-600'}`}
              onPress={handleCashOut}>
              <FontAwesome5 name="money-bill-wave" size={16} color="#fff" className="mr-2" />
              <Text className="font-semibold text-white">Cash Out ${revenue}</Text>
            </Button>
          )}
        </View>

        {/* Ticket Breakdown */}
        <View className="p-4 mx-4 mb-6 rounded-xl" style={{ backgroundColor: colors.grey5 }}>
          <Text className="mb-4 text-lg font-bold" style={{ color: colors.foreground }}>
            Ticket Breakdown
          </Text>

          {event.ticketSetup.hasLevels && event.ticketSetup.levels ? (
            event.ticketSetup.levels.map((level, index) => {
              const soldForLevel = Math.floor(level.quantity * 0.6); // Simulate 60% sold
              return (
                <View key={index} className="flex-row items-center justify-between mb-3">
                  <View className="flex-row items-center">
                    <View
                      className="items-center justify-center w-8 h-8 mr-3 rounded-full"
                      style={{
                        backgroundColor:
                          level.type === 'VIP'
                            ? '#f59e0b20'
                            : level.type === 'Premium'
                              ? '#3b82f620'
                              : '#8b5cf620',
                      }}>
                      <FontAwesome5
                        name="ticket-alt"
                        size={14}
                        color={isDark ? '#d1d5db' : '#6b7280'}
                      />
                    </View>
                    <Text className="font-medium" style={{ color: colors.foreground }}>
                      {level.type}
                    </Text>
                  </View>
                  <View className="flex-row items-center">
                    <Text style={{ color: colors.foreground }}>
                      {soldForLevel}/{level.quantity} sold
                    </Text>
                    <Text className="ml-4 font-bold" style={{ color: colors.foreground }}>
                      ${soldForLevel * level.price}
                    </Text>
                  </View>
                </View>
              );
            })
          ) : (
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <View className="items-center justify-center w-8 h-8 mr-3 rounded-full bg-violet-600/20">
                  <FontAwesome5
                    name="ticket-alt"
                    size={14}
                    color={isDark ? '#d1d5db' : '#6b7280'}
                  />
                </View>
                <Text className="font-medium" style={{ color: colors.foreground }}>
                  Standard
                </Text>
              </View>
              <View className="flex-row items-center">
                <Text style={{ color: colors.foreground }}>
                  {ticketsSold}/{event.ticketSetup.totalTickets} sold
                </Text>
                <Text className="ml-4 font-bold" style={{ color: colors.foreground }}>
                  ${revenue}
                </Text>
              </View>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Event Photo Upload Bottom Sheet */}
      <EventPhotoUploadBottomSheet
        ref={photoUploadSheetRef}
        eventId={eventId}
        onPhotoUploaded={handlePhotosUpdate}
      />
    </View>
  );
}
