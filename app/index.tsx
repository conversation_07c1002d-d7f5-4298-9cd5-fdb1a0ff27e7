import { Redirect } from 'expo-router';
import * as SecureStore from 'expo-secure-store';
import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator } from 'react-native';

import {
  checkPermissions,
  isTokenExpired,
  isTokenValid,
  loadUserProfile,
  refreshAccessToken,
} from '~/lib/tokenUtils';
import { useColorScheme } from '~/lib/useColorScheme';
import { AuthStore, UserStore } from '~/store/store';

export default function Auth() {
  const [isLoading, setIsLoading] = useState(true);
  const [redirectTo, setRedirectTo] = useState<string | null>(null);
  const { colors } = useColorScheme();

  useEffect(() => {
    checkAuthenticationStatus();
  }, []);

  const checkAuthenticationStatus = async () => {
    try {
      // Get stored tokens
      const accessToken = await SecureStore.getItemAsync('accessToken');

      // 1. Check if access token exists and is valid
      if (!accessToken || !isTokenValid(accessToken)) {
        // No valid access token, redirect to login
        (AuthStore.getState() as { login: () => void }).login();
        setRedirectTo('/Auth/onboarding');
        return;
      }

      // 2. Check if access token is expired
      if (isTokenExpired(accessToken)) {
        // Token is expired, try to refresh it
        const refreshSuccess = await refreshAccessToken();

        if (!refreshSuccess) {
          // Refresh failed, redirect to login
          await clearAuthData();
          setRedirectTo('/Auth/onboarding');
          return;
        }
      }

      // 3. At this point we have a valid access token
      // Update auth store to logged in state
      (AuthStore.getState() as { login: () => void }).login();

      // 4. Load user profile to check setup completion
      const profileLoaded = await loadUserProfile();
      if (!profileLoaded) {
        // Could not load profile, might be network issue or invalid token
        await clearAuthData();
        setRedirectTo('/Auth/onboarding');
        return;
      }

      // 5. Get user data to check if setup is complete
      const user = (UserStore.getState() as { user: any }).user;

      if (!user || !user.setupComplete) {
        // User hasn't completed profile setup
        setRedirectTo('/Auth/profileSetup');
        return;
      }

      // 6. Check if permissions are granted
      const hasPermissions = await checkPermissions();

      if (!hasPermissions) {
        // Permissions not granted, redirect to permissions screen
        setRedirectTo('/Auth/permissions');
        return;
      }

      // 7. All checks passed - user is authenticated and ready
      // Refresh the token in background (for fresh session)
      refreshAccessToken().catch(console.error);
      // Redirect to main app
      setRedirectTo('/(drawer)/(tabs)');
    } catch (error) {
      console.error('Authentication check failed:', error);
      await clearAuthData();
      setRedirectTo('/Auth/onboarding');
    } finally {
      setIsLoading(false);
    }
  };

  const clearAuthData = async () => {
    try {
      await SecureStore.deleteItemAsync('accessToken');
      await SecureStore.deleteItemAsync('refreshToken');
      await SecureStore.deleteItemAsync('userData');
      (AuthStore.getState() as { logout: () => void }).logout();
      (AuthStore.getState() as { disable: () => void }).disable();
    } catch (error) {
      console.error('Error clearing auth data:', error);
    }
  };

  if (isLoading) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: colors.background,
        }}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  if (redirectTo) {
    return <Redirect href={redirectTo as any} />;
  }

  // Fallback redirect (should not reach here)
  return <Redirect href="/Auth/onboarding" />;
}
