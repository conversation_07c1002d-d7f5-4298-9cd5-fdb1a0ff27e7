import React, { useRef, useEffect, useState } from 'react';
import {
  View,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  TextInput,
  Alert,
  Image,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import { AntDesign } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { AuthService } from '@/services/AuthService';
import { Toast } from 'toastify-react-native';

import { FormControl, FormControlError, FormControlErrorText } from '@/components/ui/form-control';
import { Text } from '~/components/nativewindui/Text';
import { HStack } from '~/components/ui/hstack';
import { Box } from '~/components/ui/box';
import { VStack } from '~/components/ui/vstack';
import { Input, InputField, InputSlot } from '~/components/ui/input';
import { Button, ButtonText } from '@/components/ui/button';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Define validation schemas
const emailSchema = z.object({
  email: z.string().email('Please enter a valid email'),
});

const passwordSchema = z
  .object({
    newPassword: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .refine((value) => /\d/.test(value), {
        message: 'Password must include at least one number',
      })
      .refine((value) => /[A-Z]/.test(value), {
        message: 'Password must include at least one uppercase letter',
      }),
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type EmailFormData = z.infer<typeof emailSchema>;
type PasswordFormData = z.infer<typeof passwordSchema>;

export default function ForgotPasswordScreen() {
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'email' | 'password'>('email');
  const [userEmail, setUserEmail] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [userID, setuserID] = useState('');

  const emailInputRef = useRef<TextInput>(null);
  const newPasswordInputRef = useRef<TextInput>(null);
  const confirmPasswordInputRef = useRef<TextInput>(null);

  useEffect(() => {}, [userID]);

  const emailForm = useForm<EmailFormData>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: '',
    },
  });

  const passwordForm = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      newPassword: '',
      confirmPassword: '',
    },
  });

  const insets = useSafeAreaInsets();

  const handleSendOTP = async (data: EmailFormData) => {
    Keyboard.dismiss();
    setIsLoading(true);

    try {
      const response = await AuthService.sendOtp(data.email);
      const currentUserID = response.body.userId; // Store the userID from response
      setuserID(currentUserID);
      setUserEmail(data.email);

      Toast.show({
        type: 'success',
        text1: 'OTP Sent',
        text2: 'A verification code has been sent to your email address.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });

      // Prompt user to enter OTP
      Alert.prompt(
        'Verify Email',
        'Enter the OTP sent to your email address.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Verify',
            onPress: async (otp) => {
              if (otp) {
                try {
                  await AuthService.verifyEmail(otp, currentUserID); // Use currentUserID instead of userID state

                  Toast.show({
                    type: 'success',
                    text1: 'Verified',
                    text2: 'Email verified successfully. Please set your new password.',
                    position: 'bottom',
                    theme: isDark ? 'dark' : 'light',
                    backgroundColor: colors.background,
                    autoHide: true,
                  });

                  // Reset the password form before transitioning
                  passwordForm.reset({
                    newPassword: '',
                    confirmPassword: '',
                  });

                  setStep('password');
                } catch (error) {
                  Toast.show({
                    type: 'error',
                    text1: 'Verification Failed',
                    text2: 'Invalid OTP. Please try again.',
                    position: 'bottom',
                    theme: isDark ? 'dark' : 'light',
                    backgroundColor: colors.background,
                    autoHide: true,
                  });
                }
              }
            },
          },
        ],
        'plain-text'
      );
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to send OTP. Please try again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async (data: PasswordFormData) => {
    Keyboard.dismiss();
    setIsLoading(true);

    try {
      await AuthService.forgotPassword(userEmail, data.newPassword);

      Toast.show({
        type: 'success',
        text1: 'Password Reset',
        text2: 'Your password has been reset successfully.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });

      // Navigate back to login
      setTimeout(() => {
        router.replace('/Auth/login');
      }, 2000);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Reset Failed',
        text2: error.message || 'Failed to reset password. Please try again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}
        className="flex-1"
        style={{ backgroundColor: colors.background }}>
        <StatusBar style={isDark ? 'light' : 'dark'} />

        <Box
          className="flex-1 px-3 pb-6"
          style={{ paddingTop: Platform.OS === 'ios' ? insets.top : insets.top + 15 }}>
          <HStack className="mb-12 items-center justify-between">
            <Pressable
              onPress={() => {
                if (step === 'password') {
                  setStep('email');
                } else {
                  router.back();
                }
              }}
              className="h-10 w-10 items-center justify-center rounded-full">
              <AntDesign name="arrowleft" size={24} color={colors.foreground} />
            </Pressable>
            <Text className="font-bold text-lg" style={{ color: colors.foreground }}>
              {step === 'email' ? 'Forgot Password' : 'Set New Password'}
            </Text>
            <Box className="w-10" />
          </HStack>

          <Box className="mb-6 items-center">
            {isDark ? (
              <Image source={require('../../assets/dark_theme_logo.png')} className="h-24 w-24" />
            ) : (
              <Image source={require('../../assets/light_theme_logo.png')} className="h-24 w-24" />
            )}
          </Box>

          {step === 'email' && (
            <>
              <Text
                className="mb-2 text-center font-bold text-2xl"
                style={{ color: colors.foreground }}>
                Reset Password
              </Text>
              <Text
                className="mb-10 px-3 text-center font-sans text-base opacity-70"
                style={{ color: colors.grey }}>
                Enter your email address and we'll send you a verification code to reset your
                password
              </Text>

              <VStack space="lg" className="mb-6 px-3">
                <FormControl isInvalid={!!emailForm.formState.errors.email}>
                  <Controller
                    name="email"
                    control={emailForm.control}
                    render={({ field: { onChange, onBlur, value } }) => (
                      <Input
                        variant="outline"
                        className="h-14 rounded-xl border-0"
                        style={{ backgroundColor: colors.grey5 }}>
                        <InputField
                          ref={emailInputRef}
                          placeholder="Email Address"
                          onBlur={onBlur}
                          onChangeText={onChange}
                          value={value}
                          autoCapitalize="none"
                          keyboardType="email-address"
                          returnKeyType="done"
                          onSubmitEditing={emailForm.handleSubmit(handleSendOTP)}
                          className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                          placeholderTextColor={isDark ? colors.grey : colors.grey}
                        />
                      </Input>
                    )}
                  />
                  <FormControlError>
                    <FormControlErrorText className="text-red-500">
                      {emailForm.formState.errors.email?.message}
                    </FormControlErrorText>
                  </FormControlError>
                </FormControl>
              </VStack>

              <Button
                className={`mx-3 mt-4 h-14 flex-row rounded-xl ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
                onPress={emailForm.handleSubmit(handleSendOTP)}
                isDisabled={isLoading}>
                <ButtonText className="font-bold text-white">
                  {isLoading ? 'Sending...' : 'Send Verification Code'}
                </ButtonText>
              </Button>
            </>
          )}
          {step === 'password' && (
            <>
              <Text
                className="mb-2 text-center font-bold text-2xl"
                style={{ color: colors.foreground }}>
                New Password
              </Text>
              <Text
                className="mb-10 px-3 text-center font-sans text-base opacity-70"
                style={{ color: colors.grey }}>
                Create a strong password for your account
              </Text>

              <VStack space="lg" className="mb-6 px-3">
                <FormControl isInvalid={!!passwordForm.formState.errors.newPassword}>
                  <Controller
                    name="newPassword"
                    control={passwordForm.control}
                    render={({ field: { onChange, onBlur, value } }) => (
                      <Input
                        variant="outline"
                        className="h-14 rounded-xl border-0"
                        style={{ backgroundColor: colors.grey5 }}>
                        <InputField
                          ref={newPasswordInputRef}
                          placeholder="New Password"
                          onBlur={onBlur}
                          onChangeText={onChange}
                          value={value}
                          secureTextEntry={!showNewPassword}
                          returnKeyType="next"
                          onSubmitEditing={() => confirmPasswordInputRef.current?.focus()}
                          blurOnSubmit={false}
                          className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                          placeholderTextColor={isDark ? colors.grey : colors.grey}
                        />
                        <InputSlot className="pr-3">
                          <Pressable onPress={() => setShowNewPassword(!showNewPassword)}>
                            <AntDesign
                              name={showNewPassword ? 'eyeo' : 'eye'}
                              size={18}
                              color={isDark ? '#9ca3af' : '#6b7280'}
                            />
                          </Pressable>
                        </InputSlot>
                      </Input>
                    )}
                  />
                  <FormControlError>
                    <FormControlErrorText className="text-red-500">
                      {passwordForm.formState.errors.newPassword?.message}
                    </FormControlErrorText>
                  </FormControlError>
                </FormControl>

                <FormControl isInvalid={!!passwordForm.formState.errors.confirmPassword}>
                  <Controller
                    name="confirmPassword"
                    control={passwordForm.control}
                    render={({ field: { onChange, onBlur, value } }) => (
                      <Input
                        variant="outline"
                        className="h-14 rounded-xl border-0"
                        style={{ backgroundColor: colors.grey5 }}>
                        <InputField
                          ref={confirmPasswordInputRef}
                          placeholder="Confirm Password"
                          onBlur={onBlur}
                          onChangeText={onChange}
                          value={value}
                          secureTextEntry={!showConfirmPassword}
                          returnKeyType="done"
                          onSubmitEditing={passwordForm.handleSubmit(handleResetPassword)}
                          className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                          placeholderTextColor={isDark ? colors.grey : colors.grey}
                        />
                        <InputSlot className="pr-3">
                          <Pressable onPress={() => setShowConfirmPassword(!showConfirmPassword)}>
                            <AntDesign
                              name={showConfirmPassword ? 'eyeo' : 'eye'}
                              size={18}
                              color={isDark ? '#9ca3af' : '#6b7280'}
                            />
                          </Pressable>
                        </InputSlot>
                      </Input>
                    )}
                  />
                  <FormControlError>
                    <FormControlErrorText className="text-red-500">
                      {passwordForm.formState.errors.confirmPassword?.message}
                    </FormControlErrorText>
                  </FormControlError>
                </FormControl>
              </VStack>

              {/* Password Requirements */}
              <View className="mb-4 px-3">
                <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  Password requirements:
                </Text>
                <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  • At least 8 characters long
                </Text>
                <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  • Include at least one number
                </Text>
                <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  • Include at least one uppercase letter
                </Text>
              </View>

              <Button
                className={`mx-3 mt-4 h-14 flex-row rounded-xl ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
                onPress={passwordForm.handleSubmit(handleResetPassword)}
                isDisabled={isLoading}>
                <ButtonText className="font-bold text-white">
                  {isLoading ? 'Resetting...' : 'Reset Password'}
                </ButtonText>
              </Button>
            </>
          )}

          <HStack className="mt-10 justify-center">
            <Text className="text-sm" style={{ color: colors.foreground }}>
              Remember your password?
            </Text>
            <Pressable onPress={() => router.push('/Auth/login')}>
              <Text
                className="ml-1 font-medium text-sm"
                style={{
                  color: isDark ? colors.primary : colors.primary,
                }}>
                Login
              </Text>
            </Pressable>
          </HStack>
        </Box>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
}
