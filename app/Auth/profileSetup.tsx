import React, { useState } from 'react';
import { View, Text, TouchableOpacity, SafeAreaView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import { AntDesign, FontAwesome5 } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';

// Import step components
import UsernameStep from '~/components/Auth/UsernameStep';
import GenderStep from '~/components/Auth/GenderStep';
import InterestsStep from '~/components/Auth/InterestsStep';
import EventTypesStep from '~/components/Auth/EventTypesStep';
import PermissionsStep from '~/components/Auth/PermissionsStep';
import CompletionStep from '~/components/Auth/CompletionStep';

// Define the user data interface
export interface ProfileData {
  username: string;
  gender: string;
  interests: string[];
  eventPreferences: string[];
  permissionsGranted: boolean;
}

export default function ProfileSetupScreen() {
  const router = useRouter();
  const { colors } = useColorScheme();

  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4; // 4 steps in the profile setup flow

  // Initialize user data with example values from the logged-in user
  const [userData, setUserData] = useState<ProfileData>({
    username: '',
    gender: '',
    interests: [],
    eventPreferences: [],
    permissionsGranted: false,
  });

  // Handle next step
  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete setup
      completeSetup();
    }
  };

  // Handle previous step
  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      // Show warning about exiting setup
      router.back();
    }
  };

  // Update user data
  const updateUserData = (data: Partial<ProfileData>) => {
    setUserData({ ...userData, ...data });
  };

  // Complete profile setup
  const completeSetup = () => {
    // Navigate to the main app
    router.replace('/(drawer)/(tabs)');
  };

  // Render current step
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <GenderStep userData={userData} updateUserData={updateUserData} onNext={nextStep} />;
      case 2:
        return (
          <EventTypesStep userData={userData} updateUserData={updateUserData} onNext={nextStep} />
        );
      case 3:
        return (
          <InterestsStep userData={userData} updateUserData={updateUserData} onNext={nextStep} />
        );
      case 4:
        return (
          <CompletionStep userData={userData} updateUserData={updateUserData} onNext={nextStep} />
        );
      default:
        return null;
    }
  };

  // Get step title
  const getStepTitle = () => {
    switch (currentStep) {
      case 1:
        return 'Select Gender';
      case 2:
        return 'Your Interests';
      case 3:
        return 'Complete';
      default:
        return 'Profile Setup';
    }
  };

  return (
    <SafeAreaView className="flex-1" style={{ backgroundColor: colors.background }}>
      <StatusBar style="dark" />

      <View className="mt-5 flex-row items-center justify-between px-3 ">
        <TouchableOpacity
          onPress={prevStep}
          className="h-10 w-10 items-center justify-center rounded-full">
          <AntDesign name="arrowleft" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text className="font-medium text-lg" style={{ color: colors.foreground }}>
          {getStepTitle()}
        </Text>
        {/*   <View className="flex-row items-center justify-center gap-1.5 px-6">
          {Array.from({ length: totalSteps }).map((_, index) => (
            <View
              key={index}
              className="h-2 rounded"
              style={{
                backgroundColor: index < currentStep ? colors.primary : colors.grey,
                width: index < currentStep ? 24 : 8,
              }}
            />
          ))}
        </View> */}
        <View className="px-6 ">
          <Text className="text-sm text-gray-500">
            Step {currentStep} of {totalSteps}
          </Text>
        </View>
      </View>

      <View className="flex-1 pt-16">{renderStep()}</View>
    </SafeAreaView>
  );
}
