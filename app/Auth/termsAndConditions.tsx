import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import { AntDesign } from '@expo/vector-icons';
import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useColorScheme } from '~/lib/useColorScheme';

export default function TermsAndConditionsScreen() {
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const insets = useSafeAreaInsets();
  return (
    <View
      className="flex-1"
      style={{
        paddingTop: Platform.OS === 'ios' ? insets.top : insets.top + 15,
        backgroundColor: colors.background,
      }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View className="flex-row items-center justify-between px-3 pb-2">
        <TouchableOpacity
          onPress={() => router.back()}
          className="items-center justify-center w-10 h-10 rounded-full">
          <AntDesign name="arrowleft" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text className="text-lg font-medium" style={{ color: colors.foreground }}>
          Terms & Conditions
        </Text>
        <View className="w-10" />
      </View>

      <ScrollView className="flex-1 px-6 pt-4" showsVerticalScrollIndicator={true}>
        <Text style={{ color: colors.foreground }} className="mb-6 text-2xl font-bold">
          Terms and Conditions
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          1. Acceptance of Terms
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          By accessing or using our Social Events application, you agree to be bound by these Terms
          and Conditions. If you disagree with any part of the terms, you may not access the
          service.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          2. User Accounts
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          When you create an account with us, you must provide accurate, complete, and updated
          information. You are responsible for safeguarding the password and for all activities that
          occur under your account.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          3. Content
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          Our Service allows you to post, link, store, share and otherwise make available certain
          information, text, graphics, videos, or other material. You are responsible for the
          content you post, including its legality, reliability, and appropriateness.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          4. Privacy Policy
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          Last Updated: 21st July 2025
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          At Ripple – Stay Connected, we are committed to protecting your privacy. This Privacy 
          Policy outlines how we collect, use, disclose, and safeguard your information when you 
          use our application and services.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-semibold">
          4.1 Information We Collect
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-2 text-base font-medium">
          Personal Information:
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-3 text-base">
          We collect information such as your name, email address, phone number, location data, 
          and payment information when you create an account, purchase tickets, or use our services.
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-2 text-base font-medium">
          Usage Data:
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          We collect details about your interaction with the app, including IP address, device 
          information, browser type, location history (if permitted), and the features or pages 
          you access.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-semibold">
          4.2 How We Use Your Information
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          • To provide, operate, and maintain our social media and event discovery services{'\n'}
          • To process transactions and manage event bookings and ticket purchases{'\n'}
          • To enable location-based features such as discovering events or people nearby{'\n'}
          • To send important updates, notifications, or promotional messages{'\n'}
          • To improve app functionality and user experience through analytics and feedback{'\n'}
          • To ensure the safety and security of our users and platform
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-semibold">
          4.3 Disclosure of Your Information
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-2 text-base font-medium">
          With Service Providers:
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-3 text-base">
          We may share your information with trusted third-party vendors who help us operate the 
          app, process payments, provide analytics, or deliver other features.
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-2 text-base font-medium">
          For Legal Reasons:
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-3 text-base">
          We may disclose your information to comply with legal obligations, enforce our terms 
          and policies, or respond to lawful requests by public authorities.
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-2 text-base font-medium">
          Business Transfers:
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          In the event of a merger, acquisition, restructuring, or sale of assets, your personal 
          information may be transferred as part of the business transaction.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-semibold">
          4.4 Data Security
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          We use reasonable administrative, technical, and physical measures to protect your 
          personal information. However, no digital transmission or storage system is completely 
          secure, and we cannot guarantee absolute security.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-semibold">
          4.5 Your Rights
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          • Access: You can request access to the personal data we hold about you.{'\n'}
          • Correction: You have the right to request that inaccurate or incomplete data be corrected.{'\n'}
          • Deletion: You may request deletion of your account and data, subject to legal or contractual obligations.{'\n'}
          • Opt-out: You can opt out of marketing emails or notifications at any time through your settings or by contacting us.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-semibold">
          4.6 Children's Privacy
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          Ripple – Stay Connected is not intended for children under the age of 16. We do not 
          knowingly collect personal information from children. If we become aware of such 
          collection, we will promptly delete the information.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-semibold">
          4.7 Changes to This Privacy Policy
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          We may update this Privacy Policy periodically. Updates will be posted on this page 
          with a new "Last Updated" date. We encourage you to review this policy regularly.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          5. Location Data
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          Our app uses location data to enhance your experience. We only collect location data with
          your explicit permission and use it to show nearby events and users. You can disable
          location services at any time through your device settings.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          6. Media Access
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          We request access to your device's media gallery to allow you to upload and share photos
          related to events. We will never access your media without your explicit action to select
          and share content.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          7. Events and Bookings
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          Our platform allows users to create, discover, and book events. We are not responsible for
          the accuracy of event details, fulfillment of events, or quality of events posted by
          users. All transactions are final and subject to the event organizer's refund policy.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          8. Termination
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          We may terminate or suspend your account immediately, without prior notice or liability,
          for any reason whatsoever, including without limitation if you breach the Terms.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          9. Changes to Terms
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          We reserve the right, at our sole discretion, to modify or replace these Terms at any
          time. If a revision is material we will try to provide at least 30 days' notice prior to
          any new terms taking effect.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          10. Contact Us
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          If you have any questions or concerns about this Privacy Policy or these Terms, please 
          reach out to us:
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-2 text-base font-medium">
          Ripple – Stay Connected Support
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-8 text-base">
          Email: <EMAIL>
        </Text>

        {/* Add some bottom padding for better scrolling experience */}
        <View className="h-8" />
      </ScrollView>
    </View>
  );
}
