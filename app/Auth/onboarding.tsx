import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { FontAwesome5 } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';

export default function OnboardingScreen() {
  const router = useRouter();
  const { colors, isDark } = useColorScheme();

  return (
    <View
      className="flex-1 "
      style={{ backgroundColor: isDark ? colors.background : colors.primary }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      {isDark ? (
        <Image
          source={require('../../assets/images/backgroundImageDark.png')}
          className="absolute h-full w-full opacity-100"
          resizeMode="contain"
        />
      ) : (
        <Image
          source={require('../../assets/images/backgroundImage.png')}
          className="absolute h-full w-full opacity-100"
          resizeMode="contain"
        />
      )}

      <View className="flex-1 justify-end p-6 pb-20 ">
        <View className="w-full">
          <TouchableOpacity
            className="mb-3 items-center rounded-xl border-white p-3"
            style={{
              backgroundColor: isDark ? colors.primary : 'white',
            }}
            onPress={() => router.push('/Auth/login')}>
            <Text
              className="font-bold text-lg text-white"
              style={{
                color: colors.foreground,
              }}>
              Login
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="items-center rounded-xl border-2 border-white bg-transparent px-3 py-[10px]"
            onPress={() => router.push('/Auth/signup')}>
            <Text className="font-bold text-lg text-white">Get Started</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}
