import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { VideoView, useVideoPlayer } from 'expo-video';
import { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
  Animated,
  ActivityIndicator,
  BackHandler,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Toast } from 'toastify-react-native';

import { useColorScheme } from '~/lib/useColorScheme';
import { FileService } from '~/services/FileService';
import { UserStore } from '~/store/store';
import { UserStoryData } from '~/types/story_type';

const STORY_DURATION = 5000; // 5 seconds per story (fallback for images)
const VIDEO_LOAD_TIMEOUT = 8000; // 8 seconds timeout for video loading
const MAX_VIDEO_RETRIES = 2; // Maximum retries for failed videos

export default function ViewStoryScreen() {
  const router = useRouter();

  const insets = useSafeAreaInsets();
  const userData = UserStore((state: any) => state.user);
  const { colors, isDark } = useColorScheme();

  // State for story data and navigation
  const [storiesData, setStoriesData] = useState<UserStoryData[]>([]);
  const [currentUserIndex, setCurrentUserIndex] = useState(0);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [paused, setPaused] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isFetchingStories, setIsFetchingStories] = useState(true);
  const [videoDuration, setVideoDuration] = useState(0);
  const [videoPosition, setVideoPosition] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [videoError, setVideoError] = useState<string | null>(null);
  const [videoRetryCount, setVideoRetryCount] = useState(0);
  const [isComponentMounted, setIsComponentMounted] = useState(true);
  const [showCompletion, setShowCompletion] = useState(false);

  const progressAnim = useRef(new Animated.Value(0)).current;
  const progressAnimation = useRef<Animated.CompositeAnimation | null>(null);
  const positionUpdateInterval = useRef<NodeJS.Timeout | null>(null);

  // Initialize video player at top level - always create but only use when needed
  const videoPlayer = useVideoPlayer(null, (player) => {
    player.loop = false;
    player.muted = false;
  });

  // Track if we need video player for current story
  const [needsVideoPlayer, setNeedsVideoPlayer] = useState(false);

  // Cleanup function to stop all playback and clear intervals
  const cleanup = useCallback(() => {
    console.log('Cleaning up story viewer...');
    setIsComponentMounted(false);

    // Stop video playback
    if (videoPlayer && needsVideoPlayer) {
      try {
        videoPlayer.pause();
        videoPlayer.replaceAsync(''); // Clear video source
      } catch (error) {
        console.log('Error cleaning up video player:', error);
      }
    }

    // Stop progress animation
    if (progressAnimation.current) {
      progressAnimation.current.stop();
      progressAnimation.current = null;
    }

    // Clear position tracking interval
    if (positionUpdateInterval.current) {
      clearInterval(positionUpdateInterval.current);
      positionUpdateInterval.current = null;
    }

    // Reset progress bar
    progressAnim.setValue(0);

    // Reset all states
    setVideoLoaded(false);
    setVideoError(null);
    setVideoRetryCount(0);
    setIsTransitioning(false);
    setLoading(false);
    setPaused(false);
  }, [videoPlayer, needsVideoPlayer, progressAnim]);

  // Component lifecycle management
  useEffect(() => {
    setIsComponentMounted(true);

    // Handle Android back button
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      handleClose();
      return true; // Prevent default behavior
    });

    return () => {
      cleanup();
      backHandler.remove();
    };
  }, [cleanup]);

  // Fetch user stories on component mount and every time screen comes into focus
  useFocusEffect(
    useCallback(() => {
      const fetchUserStories = async () => {
        if (!userData.id) {
          setFetchError('No story data found');
          setLoading(false);
          setIsFetchingStories(false);
          return;
        }

        try {
          // Reset all states when starting fresh fetch
          setStoriesData([]);
          setCurrentUserIndex(0);
          setCurrentStoryIndex(0);
          setLoading(true);
          setIsFetchingStories(true);
          setFetchError(null);
          setPaused(false);
          setVideoLoaded(false);

          // Stop any ongoing progress animation
          if (progressAnimation.current) {
            progressAnimation.current.stop();
          }

          const response = await FileService.getUserStories(userData.id);

          if (response.success && response.body && response.body.length > 0) {
            setStoriesData(response.body);
            // Always start at the first story
            setCurrentUserIndex(0);
            setCurrentStoryIndex(0);
          } else {
            setFetchError('No stories found for this user');
          }
        } catch (error) {
          console.error('Error fetching user stories:', error);
          setFetchError('Failed to load stories');
        } finally {
          setLoading(false);
          setIsFetchingStories(false);
        }
      };

      fetchUserStories();
    }, [userData.id])
  );

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
  };

  // Update video source and handle playback for current story
  useEffect(() => {
    if (
      storiesData.length > 0 &&
      currentUserIndex < storiesData.length &&
      storiesData[currentUserIndex]?.storyImages
    ) {
      const currentStory = storiesData[currentUserIndex].storyImages[currentStoryIndex];

      if (currentStory?.resourceType === 'video' && currentStory?.secureUrl) {
        // Only proceed if we have a valid video URL
        if (!currentStory.secureUrl.trim()) {
          console.log('Invalid video URL, skipping to next story');
          setTimeout(() => goToNextStory(), 1000);
          return;
        }

        console.log('Loading video:', currentStory.secureUrl);

        // Enable video player usage
        setNeedsVideoPlayer(true);

        setIsTransitioning(true);
        setVideoLoaded(false);
        setLoading(true);
        setVideoDuration(0);
        setVideoPosition(0);
        setVideoError(null);

        // Stop any ongoing progress animation
        if (progressAnimation.current) {
          progressAnimation.current.stop();
        }

        // Clear position tracking
        if (positionUpdateInterval.current) {
          clearInterval(positionUpdateInterval.current);
        }

        // Reset progress bar
        progressAnim.setValue(0);

        try {
          // Load the video with error handling
          videoPlayer.replaceAsync(currentStory.secureUrl).catch((error) => {
            console.error('Error loading video:', error);
            setVideoError('Failed to load video');
            setLoading(false);
            setIsTransitioning(false);
            // Skip to next story after a short delay
            setTimeout(() => goToNextStory(), 1500);
          });
        } catch (error) {
          console.error('Error setting video source:', error);
          setVideoError('Failed to load video');
          setLoading(false);
          setIsTransitioning(false);
          setTimeout(() => goToNextStory(), 1500);
        }

        // Fallback timeout to ensure video starts playing
        const fallbackTimeout = setTimeout(() => {
          if (!videoLoaded && !videoError) {
            console.log('Video loading timeout, skipping to next story');
            setLoading(false);
            setIsTransitioning(false);
            goToNextStory();
          }
        }, VIDEO_LOAD_TIMEOUT);

        return () => {
          clearTimeout(fallbackTimeout);
        };
      } else if (currentStory?.resourceType === 'image') {
        // For images, we don't need video loading
        setNeedsVideoPlayer(false);
        setIsTransitioning(false);
        setVideoLoaded(false);
        setLoading(false);
        setVideoDuration(0);
        setVideoPosition(0);
        setVideoError(null);

        // Clear any video-related intervals
        if (positionUpdateInterval.current) {
          clearInterval(positionUpdateInterval.current);
        }

        // Clear video source when switching to image
        try {
          videoPlayer.replaceAsync('').catch(() => {
            // Ignore errors when clearing video source
          });
        } catch (error) {
          // Ignore errors when clearing video source
        }
      }
    }
  }, [currentStoryIndex, currentUserIndex, storiesData, videoPlayer]);

  // Handle video playback state with more aggressive play attempts
  useEffect(() => {
    if (
      storiesData.length > 0 &&
      currentUserIndex < storiesData.length &&
      storiesData[currentUserIndex]?.storyImages &&
      needsVideoPlayer // Only run if we need video player for current story
    ) {
      const currentStory = storiesData[currentUserIndex].storyImages[currentStoryIndex];

      if (currentStory?.resourceType === 'video') {
        if (!paused && videoLoaded) {
          console.log('Starting video playback');
          // Try multiple times to ensure video starts playing
          videoPlayer.play();

          // Fallback: retry after a short delay
          setTimeout(() => {
            if (!paused) {
              videoPlayer.play();
            }
          }, 100);
        } else if (paused) {
          console.log('Pausing video playback');
          videoPlayer.pause();
        }
      }
    }
  }, [
    paused,
    videoLoaded,
    currentStoryIndex,
    currentUserIndex,
    storiesData,
    needsVideoPlayer,
    videoPlayer,
  ]);

  const startProgressAnimation = () => {
    const currentStory = storiesData[currentUserIndex]?.storyImages?.[currentStoryIndex];

    if (currentStory?.resourceType === 'video' && videoDuration > 0) {
      // For videos, use actual video duration
      startVideoProgressTracking();
    } else {
      // For images, use default duration
      progressAnim.setValue(0);
      progressAnimation.current = Animated.timing(progressAnim, {
        toValue: 1,
        duration: STORY_DURATION,
        useNativeDriver: false,
      });

      progressAnimation.current.start(({ finished }) => {
        if (finished && !paused) {
          goToNextStory();
        }
      });
    }
  };

  const startVideoProgressTracking = () => {
    if (positionUpdateInterval.current) {
      clearInterval(positionUpdateInterval.current);
    }

    positionUpdateInterval.current = setInterval(() => {
      if (!paused && videoDuration > 0) {
        const progress = videoPosition / videoDuration;
        progressAnim.setValue(Math.min(progress, 1));

        // Check if video is near the end
        if (progress >= 0.98) {
          goToNextStory();
        }
      }
    }, 100);
  };

  const pauseProgressAnimation = () => {
    if (progressAnimation.current) {
      progressAnimation.current.stop();
    }
    if (positionUpdateInterval.current) {
      clearInterval(positionUpdateInterval.current);
    }
  };

  const resumeProgressAnimation = () => {
    const currentStory = storiesData[currentUserIndex]?.storyImages?.[currentStoryIndex];

    if (currentStory?.resourceType === 'video' && videoDuration > 0) {
      startVideoProgressTracking();
    } else {
      const currentValue = (progressAnim as any)._value;
      const remainingDuration = STORY_DURATION * (1 - currentValue);

      progressAnimation.current = Animated.timing(progressAnim, {
        toValue: 1,
        duration: remainingDuration,
        useNativeDriver: false,
      });

      progressAnimation.current.start(({ finished }) => {
        if (finished && !paused) {
          goToNextStory();
        }
      });
    }
  };

  const goToPreviousStory = () => {
    if (currentStoryIndex > 0) {
      setCurrentStoryIndex(currentStoryIndex - 1);
    } else if (currentUserIndex > 0 && storiesData[currentUserIndex - 1]?.storyImages) {
      // Move to previous user's last story
      setCurrentUserIndex(currentUserIndex - 1);
      setCurrentStoryIndex(storiesData[currentUserIndex - 1].storyImages.length - 1);
    } else {
      // We're at the beginning, close the story viewer
      cleanup();
      router.back();
    }
  };

  const goToNextStory = () => {
    if (!isComponentMounted) return;

    // Stop current progress tracking
    if (progressAnimation.current) {
      progressAnimation.current.stop();
    }
    if (positionUpdateInterval.current) {
      clearInterval(positionUpdateInterval.current);
    }

    const currentUserStories = storiesData[currentUserIndex];

    if (
      currentUserStories?.storyImages &&
      currentStoryIndex < currentUserStories.storyImages.length - 1
    ) {
      // Move to next story of current user
      setCurrentStoryIndex(currentStoryIndex + 1);
      setVideoError(null);
      setVideoRetryCount(0);
    } else if (currentUserIndex < storiesData.length - 1) {
      // Move to next user's first story
      setCurrentUserIndex(currentUserIndex + 1);
      setCurrentStoryIndex(0);
      setVideoError(null);
      setVideoRetryCount(0);
    } else {
      // We've reached the end, show completion screen
      setShowCompletion(true);
    }
  };

  // Handle video status changes with proper error handling
  useEffect(() => {
    if (!needsVideoPlayer) return; // Only run if we need video player

    const handleStatusChange = (status: any) => {
      console.log('Video status changed:', status);

      // Handle video loaded state and get duration
      if (status.isLoaded && !videoLoaded && !videoError) {
        console.log('Video is loaded, setting videoLoaded to true');
        setVideoLoaded(true);
        setLoading(false);
        setIsTransitioning(false);

        // Get video duration if available
        if (status.durationMillis) {
          setVideoDuration(status.durationMillis);
        }

        startProgressAnimation();
      }

      // Update video position
      if (status.positionMillis !== undefined) {
        setVideoPosition(status.positionMillis);
      }

      // Update duration if it becomes available
      if (status.durationMillis && status.durationMillis !== videoDuration) {
        setVideoDuration(status.durationMillis);
      }

      // Handle video end - check multiple possible properties
      if (status.didJustFinish || status.hasFinishedPlaying || status.isFinished) {
        console.log('Video playback ended');
        goToNextStory();
      }

      // Handle video errors with more specific handling
      if (status.error) {
        console.error('Video error:', status.error);
        setVideoError(status.error.message || 'Unknown video error');
        setLoading(false);
        setIsTransitioning(false);
        setVideoLoaded(false);

        // For "Cannot Open" errors, retry once if we haven't exceeded max retries
        if (status.error.message?.includes('Cannot Open') && videoRetryCount < MAX_VIDEO_RETRIES) {
          console.log(`Retrying video load (attempt ${videoRetryCount + 1}/${MAX_VIDEO_RETRIES})`);
          setVideoRetryCount((prev) => prev + 1);

          // Retry after a short delay
          setTimeout(() => {
            const currentStory = storiesData[currentUserIndex]?.storyImages?.[currentStoryIndex];
            if (currentStory?.resourceType === 'video' && currentStory?.secureUrl) {
              setVideoError(null);
              setLoading(true);
              setIsTransitioning(true);
              videoPlayer.replaceAsync(currentStory.secureUrl).catch((retryError) => {
                console.error('Retry failed:', retryError);
                setTimeout(() => goToNextStory(), 1000);
              });
            }
          }, 2000);
        } else {
          // Skip to next story on error after max retries
          setTimeout(() => {
            goToNextStory();
          }, 1500);
        }
      }
    };

    // Add event listener for expo-video status changes
    let statusSubscription: any = null;

    try {
      statusSubscription = videoPlayer.addListener('statusChange', handleStatusChange);
    } catch (error) {
      console.error('Failed to add status change listener:', error);
    }

    return () => {
      // Cleanup event listeners
      if (statusSubscription?.remove) {
        statusSubscription.remove();
      }
      if (positionUpdateInterval.current) {
        clearInterval(positionUpdateInterval.current);
      }
    };
  }, [
    needsVideoPlayer,
    videoPlayer,
    videoLoaded,
    storiesData,
    currentUserIndex,
    currentStoryIndex,
    videoDuration,
    isTransitioning,
    videoError,
    videoRetryCount,
  ]);

  const handlePress = (event: { nativeEvent: { locationX: number } }) => {
    const { locationX } = event.nativeEvent;
    const screenWidth = Dimensions.get('window').width;

    if (locationX < screenWidth / 3) {
      goToPreviousStory();
    } else if (locationX > (screenWidth * 2) / 3) {
      goToNextStory();
    } else {
      // Toggle pause/play
      if (paused) {
        setPaused(false);
        resumeProgressAnimation();
      } else {
        setPaused(true);
        pauseProgressAnimation();
      }
    }
  };

  useEffect(() => {
    // Reset loading state when story or user changes
    setLoading(true);
    setIsTransitioning(true);
    if (progressAnimation.current) {
      progressAnimation.current.stop();
    }
    if (positionUpdateInterval.current) {
      clearInterval(positionUpdateInterval.current);
    }
    // Reset progress bar
    progressAnim.setValue(0);
  }, [currentStoryIndex, currentUserIndex]);

  const handleClose = () => {
    cleanup();
    // Safe navigation - go to home if can't go back
    try {
      if (router.canGoBack()) {
        router.back();
      } else {
        router.replace('/(drawer)/(tabs)');
      }
    } catch (error) {
      console.error('Navigation error:', error);
      router.replace('/(drawer)/(tabs)');
    }
  };

  const handleImageLoad = () => {
    console.log('Image loaded, starting progress animation');
    setLoading(false);
    setIsTransitioning(false);
    startProgressAnimation();
  };

  const handleDeleteStory = async () => {
    const currentUserStoryData = storiesData[currentUserIndex];
    const currentStoryUpload = currentUserStoryData.storyImages[currentStoryIndex];

    if (!currentStoryUpload || isDeleting) return;

    try {
      setIsDeleting(true);

      // Call delete story API
      await FileService.deleteStory({
        storyId: currentUserStoryData.id,
        userId: userData.id,
      });

      // Remove the deleted story from local state
      const updatedStoriesData = [...storiesData];
      updatedStoriesData[currentUserIndex].storyImages.splice(currentStoryIndex, 1);

      // If no more stories for this user, remove the user data
      if (updatedStoriesData[currentUserIndex].storyImages.length === 0) {
        updatedStoriesData.splice(currentUserIndex, 1);

        // If no more users with stories, go back
        if (updatedStoriesData.length === 0) {
          cleanup();
          router.back();
          return;
        }

        // Adjust indices if we removed a user
        if (currentUserIndex >= updatedStoriesData.length) {
          setCurrentUserIndex(updatedStoriesData.length - 1);
          setCurrentStoryIndex(0);
        } else {
          setCurrentStoryIndex(0);
        }
      } else {
        // Adjust story index if we're at the last story
        if (currentStoryIndex >= updatedStoriesData[currentUserIndex].storyImages.length) {
          setCurrentStoryIndex(updatedStoriesData[currentUserIndex].storyImages.length - 1);
        }
      }

      setStoriesData(updatedStoriesData);

      Toast.show({
        type: 'success',
        text1: 'Story Deleted',
        text2: 'Your story has been deleted successfully',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } catch (error) {
      console.error('Error deleting story:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to delete story. Please try again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Reset video loaded state when story changes
  useEffect(() => {
    setVideoLoaded(false);
    setVideoDuration(0);
    setVideoPosition(0);
    setVideoError(null);
    setVideoRetryCount(0);
  }, [currentStoryIndex]);

  // Show loading state for initial fetch
  if (isFetchingStories) {
    return (
      <View className="items-center justify-center flex-1 bg-black">
        <StatusBar style="light" />
        <ActivityIndicator size="large" color="#fff" />
        <Text className="mt-4 font-medium text-white">Loading stories...</Text>
      </View>
    );
  }

  // Show loading state
  if (loading && !storiesData.length) {
    return (
      <View className="items-center justify-center flex-1 bg-black">
        <StatusBar style="light" />
        <ActivityIndicator size="large" color="#fff" />
        <Text className="mt-4 font-medium text-white">Preparing story...</Text>
      </View>
    );
  }

  // Show error state
  if (fetchError || !storiesData.length) {
    return (
      <View className="items-center justify-center flex-1 bg-black">
        <StatusBar style="light" />
        <Text className="font-medium text-white">{fetchError || 'No stories found'}</Text>
        <TouchableOpacity
          className="px-6 py-3 mt-4 rounded-full bg-violet-600"
          onPress={handleClose}>
          <Text className="font-medium text-white">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Show completion screen when all stories are finished
  if (showCompletion) {
    return (
      <View className="items-center justify-center flex-1 bg-black">
        <StatusBar style="light" />

        <View className="items-center px-6">
          <View className="items-center justify-center w-20 h-20 mb-6 rounded-full bg-violet-600">
            <Ionicons name="checkmark" size={40} color="white" />
          </View>

          <Text className="mb-2 text-2xl font-bold text-white">Stories Complete!</Text>
          <Text className="mb-8 text-center text-gray-300">
            You've viewed all available stories
          </Text>

          <View className="flex-row space-x-4">
            <TouchableOpacity
              className="px-6 py-3 bg-gray-700 rounded-full"
              onPress={() => {
                setShowCompletion(false);
                setCurrentUserIndex(0);
                setCurrentStoryIndex(0);
                setVideoError(null);
                setVideoRetryCount(0);
              }}>
              <Text className="font-medium text-white">Rewatch</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="px-6 py-3 rounded-full bg-violet-600"
              onPress={() => router.back('/(drawer)/(tabs)')}>
              <Text className="font-medium text-white">Go Home</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  const currentUserStoryData = storiesData[currentUserIndex];
  const currentStoryUpload = currentUserStoryData?.storyImages?.[currentStoryIndex];

  const totalStories = storiesData.reduce(
    (sum, userData) => sum + (userData?.storyImages?.length || 0),
    0
  );
  const currentStoryNumber =
    storiesData
      .slice(0, currentUserIndex)
      .reduce((sum, userData) => sum + (userData?.storyImages?.length || 0), 0) +
    currentStoryIndex +
    1;

  return (
    <View className="flex-1 bg-black">
      <StatusBar style={isDark ? 'light' : 'dark'} />

      {/* Progress bars */}
      <View
        className="absolute left-0 right-0 z-10 flex-row px-2 pb-2"
        style={{ paddingTop: insets.top + 10 }}>
        {currentUserStoryData?.storyImages?.map((_, index) => (
          <View key={index} className="mx-0.5 h-1 flex-1 overflow-hidden rounded-full bg-gray-300">
            {index < currentStoryIndex ? (
              <View className="h-full bg-white" />
            ) : index === currentStoryIndex ? (
              <Animated.View
                className="h-full"
                style={{
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                  backgroundColor: colors.primary,
                }}
              />
            ) : null}
          </View>
        )) || []}
      </View>

      {/* User info */}
      <View
        className="absolute left-0 right-0 z-10 flex-row items-center justify-between px-4 top-5"
        style={{ paddingTop: insets.top + 20 }}>
        <View className="flex-row items-center">
          <TouchableOpacity onPress={handleClose} className="mr-3">
            <Ionicons name="chevron-back" size={24} color={colors.foreground} />
          </TouchableOpacity>
          {/* Clickable user header to navigate to profile */}
          <TouchableOpacity
            className="flex-row items-center"
            onPress={() => {
              if (currentUserStoryData?.user) {
                router.push({
                  pathname: '/Auth/viewProfile',
                  params: {
                    userId: currentUserStoryData.user.id,
                    email: currentUserStoryData.user.email,
                  },
                });
              }
            }}>
            <Image
              source={{
                uri:
                  currentUserStoryData?.user?.profilePicture &&
                  currentUserStoryData.user.profilePicture.length > 0
                    ? currentUserStoryData.user.profilePicture[
                        currentUserStoryData.user.profilePicture.length - 1
                      ].secureUrl
                    : 'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
              }}
              className="w-8 h-8 mr-2 rounded-full"
            />
            <View>
              <Text className="font-medium " style={{ color: colors.foreground }}>
                {currentUserStoryData?.user?.fullName || 'Unknown User'}
              </Text>
              <Text className="text-xs" style={{ color: colors.foreground }}>
                {currentStoryUpload?.createdAt ? formatTimeAgo(currentStoryUpload.createdAt) : ''}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
        <View className="flex-row items-center ">
          <Text className="mr-3 text-xs" style={{ color: colors.foreground }}>
            {currentStoryNumber} / {totalStories}
          </Text>
          {/* Only show delete button if viewing own story */}
          {currentUserStoryData?.user?.id === userData.id && (
            <TouchableOpacity
              onPress={handleDeleteStory}
              disabled={isDeleting}
              className="items-center justify-center w-8 h-8 bg-red-500 rounded-full">
              {isDeleting ? (
                <ActivityIndicator size="small" color={colors.foreground} />
              ) : (
                <Ionicons name="trash-outline" size={16} color={colors.foreground} />
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Story content */}
      <TouchableOpacity
        activeOpacity={1}
        onPress={handlePress}
        className="justify-center flex-1"
        style={{
          backgroundColor: colors.background,
        }}>
        {(loading || isTransitioning) && (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'black',
              justifyContent: 'center',
              alignItems: 'center',
              zIndex: 10,
            }}>
            <ActivityIndicator size="large" color="#fff" />
          </View>
        )}

        {currentStoryUpload?.resourceType === 'image' ? (
          <Image
            source={{ uri: currentStoryUpload?.secureUrl }}
            style={{
              width: Dimensions.get('window').width,
              height: Dimensions.get('window').height,
            }}
            resizeMode="contain"
            onLoad={handleImageLoad}
          />
        ) : (
          needsVideoPlayer && (
            <VideoView
              player={videoPlayer}
              style={{
                width: Dimensions.get('window').width,
                height: Dimensions.get('window').height,
                opacity: isTransitioning ? 0 : 1,
              }}
              contentFit="cover"
              allowsFullscreen={false}
              allowsPictureInPicture={false}
            />
          )
        )}
      </TouchableOpacity>
    </View>
  );
}
