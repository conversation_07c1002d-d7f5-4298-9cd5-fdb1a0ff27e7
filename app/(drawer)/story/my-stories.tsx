import React, { useRef, useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Image, FlatList, ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation, useRouter, useFocusEffect } from 'expo-router';
import { <PERSON><PERSON><PERSON>, Feather } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Toast } from 'toastify-react-native';

import { useColorScheme } from '~/lib/useColorScheme';
import { Story, StoryViewer } from '~/types/story_type';
import ViewersBottomSheet, {
  ViewersBottomSheetHandle,
} from '~/components/Story/ViewersBottomSheet';
import { FileService } from '~/services/FileService';
import { UserStore } from '~/store/store';

export default function MyStoriesScreen() {
  const navigation = useNavigation();
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const insets = useSafeAreaInsets();
  const viewersBottomSheetRef = useRef<ViewersBottomSheetHandle>(null);
  const user = UserStore((state: any) => state.user);

  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch stories from API
  const fetchStories = async () => {
    try {
      setLoading(true);
      const response = await FileService.getStories({ userId: user.id });
      if (response.success && response.body) {
        setStories(response.body);
      }
    } catch (error) {
      console.error('Error fetching stories:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load stories',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Refresh stories
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchStories();
    setRefreshing(false);
  };

  // Load stories on component mount and when screen is focused
  useEffect(() => {
    fetchStories();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      fetchStories();
    }, [])
  );

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
  };

  const formatTimeLeft = (expiresAt: string) => {
    const expiryDate = new Date(expiresAt);
    const now = new Date();
    const diffInSeconds = Math.floor((expiryDate.getTime() - now.getTime()) / 1000);

    if (diffInSeconds < 0) {
      return 'Expired';
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m left`;
    } else {
      return `${Math.floor(diffInSeconds / 3600)}h left`;
    }
  };

  const handleCreateStory = () => {
    router.push('/story/create');
  };

  const handleViewStory = (story: Story) => {
    // Navigate to view story screen with the story data
    router.push({
      pathname: '/story/view',
      params: { storyId: story.id },
    });
  };

  const handleViewViewers = (viewers: StoryViewer[]) => {
    viewersBottomSheetRef.current?.present(viewers);
  };

  const handleDeleteStory = async (storyId: number) => {
    try {
      await FileService.deleteStory({ storyId });
      // Refresh stories after deletion
      await fetchStories();
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Story deleted successfully',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } catch (error) {
      console.error('Error deleting story:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to delete story',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    }
  };

  const isStoryActive = (story: Story) => {
    const expiryDate = new Date(story.expiresAt);
    const now = new Date();
    return expiryDate > now;
  };

  const renderStoryItem = ({ item }: { item: Story }) => {
    const isActive = isStoryActive(item);

    return (
      <View
        className={`mb-4 overflow-hidden rounded-xl ${isActive ? 'border-violet-600' : 'border-gray-300'}`}
        style={{
          borderWidth: 1,
          backgroundColor: isDark ? colors.grey6 : colors.grey5,
          shadowColor: isDark ? '#000' : '#888',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 2,
        }}>
        <TouchableOpacity
          className="flex-row items-center p-4"
          onPress={() => handleViewStory(item)}>
          <View className="relative mr-3">
            {isActive ? (
              <View className="rounded-full bg-violet-600 p-0.5">
                <Image
                  source={{ uri: item.mediaUrl }}
                  className="h-16 w-16 rounded-full"
                  resizeMode="cover"
                />
              </View>
            ) : (
              <Image
                source={{ uri: item.mediaUrl }}
                className="h-16 w-16 rounded-full"
                resizeMode="cover"
              />
            )}
          </View>
          <View className="flex-1">
            <Text className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
              {item.caption || 'My story'}
            </Text>
            {/*  <View className="flex-row items-center mt-1">
              <TouchableOpacity
                className="flex-row items-center px-2 py-1 bg-gray-100 rounded-full dark:bg-gray-800"
                onPress={() => handleViewViewers(item.viewers)}>
                <Ionicons name="eye-outline" size={14} color={isDark ? '#bbb' : '#777'} />
                <Text className={`ml-1 text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  {item.viewers.length} {item.viewers.length === 1 ? 'view' : 'views'}
                </Text>
              </TouchableOpacity>
              <Text className={`ml-3 text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                {isActive ? formatTimeLeft(item.expiresAt) : formatTimeAgo(item.createdAt)}
              </Text>
            </View> */}
          </View>

          {/* Delete button for active stories */}
          {isActive && (
            <TouchableOpacity className="ml-2 p-2" onPress={() => handleDeleteStory(item.id)}>
              <Ionicons name="trash-outline" size={20} color={isDark ? '#ff6b6b' : '#e74c3c'} />
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View className="flex-1" style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View
        className="flex-row items-center justify-between px-4 pb-4"
        style={{ paddingTop: insets.top + 10 }}>
        <View className="flex-row items-center">
          <TouchableOpacity className="p-2" onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={isDark ? '#fff' : '#000'} />
          </TouchableOpacity>
          <Text className={`ml-2 font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            My stories
          </Text>
        </View>
        <TouchableOpacity className="rounded-full bg-violet-600 p-2" onPress={handleCreateStory}>
          <Ionicons name="add" size={20} color="#fff" />
        </TouchableOpacity>
      </View>

      <View className="mb-4 px-4">
        <Text className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
          Your status updates are visible for 24 hours
        </Text>
      </View>

      {loading ? (
        <View className="flex-1 items-center justify-center py-20">
          <ActivityIndicator size="large" color={colors.primary} />
          <Text className={`mt-4 ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            Loading stories...
          </Text>
        </View>
      ) : stories.length > 0 ? (
        <FlatList
          data={stories}
          renderItem={renderStoryItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={{ paddingHorizontal: 16, paddingBottom: 20 }}
          showsVerticalScrollIndicator={false}
          refreshing={refreshing}
          onRefresh={handleRefresh}
        />
      ) : (
        <View className="flex-1 items-center justify-center py-20">
          <Ionicons name="images-outline" size={60} color={isDark ? colors.grey3 : colors.grey4} />
          <Text
            className={`mt-4 text-center font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            No Stories
          </Text>
          <Text className={`mt-2 text-center ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            Your stories will appear here
          </Text>
          <TouchableOpacity
            className="mt-6 rounded-full bg-violet-600 px-6 py-3"
            onPress={handleCreateStory}>
            <Text className="font-medium text-white">Create Story</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Viewers Bottom Sheet */}
      <ViewersBottomSheet ref={viewersBottomSheetRef} />
    </View>
  );
}
