import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from 'expo-router';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';

// Mock FAQ data
const faqItems = [
  {
    id: 1,
    question: 'How do I create an event?',
    answer:
      "To create a new event, navigate to the Events tab and tap on the '+' button in the bottom right corner. Follow the prompts to fill in event details, location, date, and other preferences.",
  },
  {
    id: 2,
    question: 'How do I find events near me?',
    answer:
      'Open the app and events near your location will automatically appear on the map. You can also use the search function to find specific events or filter events by category using the filter button.',
  },
  {
    id: 3,
    question: 'Can I invite friends to an event?',
    answer:
      "Yes! When viewing an event, tap on the 'Invite' button to share the event with friends through various platforms or directly with other app users.",
  },
  {
    id: 4,
    question: 'How do I purchase tickets?',
    answer:
      "When viewing an event that requires tickets, tap on the 'Get Tickets' button. You'll be guided through the purchasing process, including selecting ticket type and payment options.",
  },
  {
    id: 5,
    question: 'How do I update my profile information?',
    answer:
      "Go to your profile by tapping on your profile picture in the drawer menu, then tap 'Edit Profile' to modify your personal information, interests, and profile picture.",
  },
];

export default function HelpScreen() {
  const navigation = useNavigation();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [expandedId, setExpandedId] = React.useState(null);

  const toggleItem = (id) => {
    setExpandedId(expandedId === id ? null : id);
  };

  return (
    <View className={`flex-1 pt-12 `} style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View className="flex-row items-center justify-between px-4 pb-4">
        <TouchableOpacity className="p-2" onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={isDark ? '#fff' : '#000'} />
        </TouchableOpacity>
        <Text className={`font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
          Help Center
        </Text>
        <View className="w-10" />
      </View>

      <ScrollView className="px-4 pb-10">
        <View className="mb-8">
          <Text className={`mb-3 font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
            Need Help?
          </Text>
          <Text
            className={`mb-6 text-base leading-[22px] ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
            Our support team is ready to assist you with any questions or issues you may have.
          </Text>

          <TouchableOpacity
            className={`mb-3 flex-row items-center rounded-xl p-4 `}
            style={{ backgroundColor: colors.grey5 }}>
            <Ionicons
              name="mail-outline"
              size={22}
              color={isDark ? '#fff' : '#000'}
              className="mr-3"
            />
            <Text className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
              Email Support
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className={`flex-row items-center rounded-xl p-4 `}
            style={{ backgroundColor: colors.grey5 }}>
            <Ionicons
              name="chatbubble-outline"
              size={22}
              color={isDark ? '#fff' : '#000'}
              className="mr-3"
            />
            <Text className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
              Live Chat
            </Text>
          </TouchableOpacity>
        </View>

        <View className="mb-6">
          <Text className={`mb-3 font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
            Frequently Asked Questions
          </Text>

          {faqItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              className={`border-b py-4 ${isDark ? 'border-gray-800' : 'border-gray-100'}`}
              onPress={() => toggleItem(item.id)}>
              <View className="flex-row items-center justify-between">
                <Text
                  className={`flex-1 pr-4 font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
                  {item.question}
                </Text>
                <MaterialIcons
                  name={expandedId === item.id ? 'remove' : 'add'}
                  size={24}
                  color={isDark ? '#bbb' : '#666'}
                />
              </View>

              {expandedId === item.id && (
                <Text
                  className={`mt-3 text-sm leading-5 ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                  {item.answer}
                </Text>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );
}
