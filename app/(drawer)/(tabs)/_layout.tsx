import { Tabs, useRouter } from 'expo-router';
import { Text, TouchableOpacity, View } from 'react-native';
import Svg, { Path, G, Circle } from 'react-native-svg';

import { useColorScheme } from '~/lib/useColorScheme';

// Home icons
const HomeSolid = ({ color }: { color: string }) => (
  <Svg width="30" height="30" viewBox="0 0 24 24">
    <G fill="none" fill-rule="evenodd">
      <Path d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z" />
      <Path
        fill={color}
        d="M13.228 2.688a2 2 0 0 0-2.456 0l-8.384 6.52C1.636 9.795 2.05 11 3.003 11h1.092l.82 8.199A2 2 0 0 0 6.905 21h10.19a2 2 0 0 0 1.99-1.801l.82-8.199h1.092c.952 0 1.368-1.205.615-1.791l-8.384-6.52ZM12 16a3 3 0 1 0 0-6a3 3 0 0 0 0 6"
      />
    </G>
  </Svg>
);

const HomeOutline = ({ color }: { color: string }) => (
  <Svg width="30" height="30" viewBox="0 0 24 24">
    <G fill="none" fill-rule="evenodd">
      <Path d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z" />
      <Path
        fill={color}
        d="M13.228 2.688a2 2 0 0 0-2.456 0l-8.384 6.52C1.636 9.795 2.05 11 3.003 11h1.092l.82 8.199A2 2 0 0 0 6.905 21h10.19a2 2 0 0 0 1.99-1.801l.82-8.199h1.092c.952 0 1.368-1.205.615-1.791l-8.384-6.52ZM5.996 9.91a1 1 0 0 0-.37-.684L12 4.267l6.374 4.958a1 1 0 0 0-.37.684L17.095 19H6.905zM10.5 13a1.5 1.5 0 1 1 3 0a1.5 1.5 0 0 1-3 0M12 9.5a3.5 3.5 0 1 0 0 7a3.5 3.5 0 0 0 0-7"
      />
    </G>
  </Svg>
);

// Placeholder SVG components for other tabs - you'll want to replace these with your actual SVGs
const DiscoverSolid = ({ color }: { color: string }) => (
  <Svg width="30" height="30" viewBox="0 0 24 24">
    <Path
      fill={color}
      d="m8.375 16.25l5.05-1.45q.5-.15.863-.513t.512-.862l1.45-5.05q.075-.275-.137-.488t-.488-.137l-5.05 1.45q-.5.15-.862.513t-.513.862l-1.45 5.05q-.075.275.138.488t.487.137M12 13.5q-.625 0-1.062-.437T10.5 12t.438-1.062T12 10.5t1.063.438T13.5 12t-.437 1.063T12 13.5m0 8.5q-2.075 0-3.9-.788t-3.175-2.137T2.788 15.9T2 12t.788-3.9t2.137-3.175T8.1 2.788T12 2t3.9.788t3.175 2.137T21.213 8.1T22 12t-.788 3.9t-2.137 3.175t-3.175 2.138T12 22"
    />
  </Svg>
);

const DiscoverOutline = ({ color }: { color: string }) => (
  <Svg width="30" height="30" viewBox="0 0 24 24">
    <Path
      fill={color}
      d="m8.375 16.25l5.05-1.45q.5-.15.863-.513t.512-.862l1.45-5.05q.075-.275-.137-.488t-.488-.137l-5.05 1.45q-.5.15-.862.513t-.513.862l-1.45 5.05q-.075.275.138.488t.487.137M12 13.5q-.625 0-1.062-.437T10.5 12t.438-1.062T12 10.5t1.063.438T13.5 12t-.437 1.063T12 13.5m0 8.5q-2.075 0-3.9-.788t-3.175-2.137T2.788 15.9T2 12t.788-3.9t2.137-3.175T8.1 2.788T12 2t3.9.788t3.175 2.137T21.213 8.1T22 12t-.788 3.9t-2.137 3.175t-3.175 2.138T12 22m0-2q3.325 0 5.663-2.337T20 12t-2.337-5.663T12 4T6.337 6.338T4 12t2.338 5.663T12 20m0-8"
    />
  </Svg>
);

const AddEventIcon = ({ color }: { color: string }) => (
  <Svg width="45" height="45" viewBox="0 0 24 24">
    <Path
      fill={color}
      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"
    />
  </Svg>
);

const ChatSolid = ({ color }: { color: string }) => (
  <Svg width="30" height="30" viewBox="0 0 28 28">
    <Path
      fill={color}
      d="M2 14C2 7.373 7.373 2 14 2s12 5.373 12 12s-5.373 12-12 12a11.95 11.95 0 0 1-5.637-1.404l-4.77 1.357a1.25 1.25 0 0 1-1.544-1.544l1.356-4.77A11.95 11.95 0 0 1 2 14m7.5-2.25c0 .414.336.75.75.75h7.5a.75.75 0 0 0 0-1.5h-7.5a.75.75 0 0 0-.75.75m.75 3.75a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5z"
    />
  </Svg>
);

const ChatOutline = ({ color }: { color: string }) => (
  <Svg width="30" height="30" viewBox="0 0 12 12">
    <Path
      fill={color}
      d="M4 5.5a.5.5 0 0 1 .5-.5h3a.5.5 0 1 1 0 1h-3a.5.5 0 0 1-.5-.5M4.5 7a.5.5 0 0 0 0 1h2a.5.5 0 0 0 0-1zM1 6a5 5 0 1 1 2.59 4.382l-1.944.592a.5.5 0 0 1-.624-.624l.592-1.947A5 5 0 0 1 1 6m5-4a4 4 0 0 0-3.417 6.08a.5.5 0 0 1 .051.406l-.383 1.259l1.257-.383a.5.5 0 0 1 .407.052A4 4 0 1 0 6 2"
    />
  </Svg>
);

const NotificationsSolid = ({ color }: { color: string }) => (
  <Svg width={30} height={30} viewBox="0 0 24 24">
    <Path
      fill={color}
      d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4"></Path>
  </Svg>
);

const NotificationsOutline = ({ color }: { color: string }) => (
  <Svg width={30} height={30} viewBox="0 0 24 24">
    <Path
      fill={color}
      d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 2a2 2 0 0 0-2 2a2 2 0 0 0 2 2a2 2 0 0 0 2-2a2 2 0 0 0-2-2m0 7c2.67 0 8 1.33 8 4v3H4v-3c0-2.67 5.33-4 8-4m0 1.9c-2.97 0-6.1 1.46-6.1 2.1v1.1h12.2V17c0-.64-3.13-2.1-6.1-2.1"></Path>
  </Svg>
);

export default function TabLayout() {
  const { colors } = useColorScheme();
  const router = useRouter();
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: colors.foreground,
        tabBarInactiveTintColor: colors.grey,
        tabBarShowLabel: false,
        tabBarStyle: {
          paddingTop: 5,
          borderTopWidth: 0.5,
          backgroundColor: colors.background,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, focused }) =>
            focused ? <HomeSolid color={color} /> : <HomeOutline color={color} />,
        }}
      />
      <Tabs.Screen
        name="discover"
        options={{
          title: 'Explore',
          tabBarIcon: ({ color, focused }) =>
            focused ? <DiscoverSolid color={color} /> : <DiscoverOutline color={color} />,
        }}
      />
      <Tabs.Screen
        name="addEvent"
        options={{
          tabBarButton: (props) => (
            <View className="items-center justify-center pt-[0rem]">
              <TouchableOpacity
                className=""
                {...props}
                style={{}}
                onPress={() => {
                  router.push('/Events/createEvent');
                }}>
                <AddEventIcon color={'gray'} />
              </TouchableOpacity>
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="chat"
        options={{
          title: 'Chat',
          tabBarIcon: ({ color, focused }) =>
            focused ? <ChatSolid color={color} /> : <ChatOutline color={color} />,
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, focused }) =>
            focused ? <NotificationsSolid color={color} /> : <NotificationsOutline color={color} />,
        }}
      />
    </Tabs>
  );
}
