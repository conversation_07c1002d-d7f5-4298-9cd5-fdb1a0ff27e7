import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, TouchableOpacity, Image, FlatList, ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useFocusEffect, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import { FriendService } from '~/services/FriendService';
import { UserStore } from '~/store/store';
import { Toast } from 'toastify-react-native';

interface SentFriendRequest {
  id: string;
  requesteeId: string;
  requesteeName: string;
  requesteeEmail: string;
  requesteeAvatar: string;
  createdAt: string;
  status: string;
}

export default function SentRequestsScreen() {
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const user = UserStore((state: any) => state.user);

  const [requests, setRequests] = useState<SentFriendRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useFocusEffect(
    useCallback(() => {
      fetchRequests();
    }, [user?.id])
  );

  const fetchRequests = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const response = await FriendService.getSentFriendRequests(user.id);

      if (
        response.success &&
        response.body &&
        Array.isArray(response.body) &&
        response.body.length > 0
      ) {
        const requestsData = response.body.map((request: any) => ({
          id: request.id.toString(),
          requesteeId: request.requestee.id.toString(),
          requesteeName:
            request.requestee?.fullName || request.requestee?.username || 'Unknown User',
          requesteeEmail: request.requestee?.email || '', // Add email field
          requesteeAvatar:
            (request.requestee?.profilePicture?.length > 0
              ? request.requestee.profilePicture[request.requestee.profilePicture.length - 1]
                  ?.secureUrl
              : null) ||
            'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',

          createdAt: new Date(request.createdAt).toLocaleDateString(),
          status: request.status,
        }));

        // Sort requests: pending first, then accepted, then rejected
        const sortedRequests = requestsData.sort((a: SentFriendRequest, b: SentFriendRequest) => {
          const statusOrder = { pending: 0, accepted: 1, rejected: 2 };
          return (
            (statusOrder[a.status.toLowerCase() as keyof typeof statusOrder] ?? 3) -
            (statusOrder[b.status.toLowerCase() as keyof typeof statusOrder] ?? 3)
          );
        });

        setRequests(sortedRequests);
      } else {
        setRequests([]);
      }
    } catch (error) {
      console.error('Error fetching sent friend requests:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load sent requests',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchRequests();
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'accepted':
        return '#22c55e'; // green
      case 'rejected':
        return '#ef4444'; // red
      case 'pending':
      default:
        return colors.primary; // primary color
    }
  };

  const getStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case 'accepted':
        return 'Accepted';
      case 'rejected':
        return 'Rejected';
      case 'pending':
      default:
        return 'Pending';
    }
  };

  const renderRequestItem = ({ item }: { item: SentFriendRequest }) => {
    return (
      <View
        className="flex-row items-center px-4 py-4 border-b"
        style={{ borderBottomColor: colors.grey5 }}>
        <TouchableOpacity
          onPress={() => {
            router.push({
              pathname: '/Auth/viewProfile',
              params: {
                userId: item.requesteeId,
                email: item.requesteeEmail,
              },
            });
          }}>
          <Image source={{ uri: item.requesteeAvatar }} className="rounded-full h-14 w-14" />
        </TouchableOpacity>
        <View className="flex-1 ml-3">
          <Text
            className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}
            numberOfLines={1}>
            {item.requesteeName}
          </Text>
          <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            Sent on {item.createdAt}
          </Text>
        </View>
        <View className="items-end">
          <View
            className="px-3 py-1 rounded-full"
            style={{ backgroundColor: getStatusColor(item.status) + '20' }}>
            <Text className="text-xs font-medium" style={{ color: getStatusColor(item.status) }}>
              {getStatusText(item.status)}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View className="flex-1" style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View className="flex-row items-center justify-between px-4 pt-12 pb-4">
        <TouchableOpacity className="p-2" onPress={() => router.replace('/(drawer)/friends')}>
          <Ionicons name="arrow-back" size={24} color={isDark ? '#fff' : '#000'} />
        </TouchableOpacity>
        <Text className={`font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
          Sent Requests
        </Text>
        <View className="w-10" />
      </View>

      {loading ? (
        <View className="items-center justify-center flex-1">
          <ActivityIndicator size="large" color={colors.primary} />
          <Text className={`mt-4 ${isDark ? 'text-white' : 'text-black'}`}>
            Loading requests...
          </Text>
        </View>
      ) : requests.length > 0 ? (
        <FlatList
          data={requests}
          renderItem={renderRequestItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ paddingBottom: 40 }}
          showsVerticalScrollIndicator={false}
          refreshing={refreshing}
          onRefresh={handleRefresh}
        />
      ) : (
        <View className="items-center justify-center flex-1 px-4">
          <Ionicons
            name="paper-plane-outline"
            size={60}
            color={isDark ? colors.grey3 : colors.grey4}
          />
          <Text
            className={`mt-4 text-center font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            No Sent Requests
          </Text>
          <Text className={`mt-2 text-center ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            You haven't sent any friend requests yet
          </Text>
        </View>
      )}
    </View>
  );
}
