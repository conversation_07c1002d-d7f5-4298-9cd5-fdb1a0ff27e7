import React, { useState, useRef, useMemo, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  StyleSheet,
  Pressable,
  Dimensions,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation, router } from 'expo-router';
import { Ionicons, MaterialCommunityIcons, FontAwesome, AntDesign } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import BottomSheet, {
  BottomSheetView,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '~/components/RenderBackdrop';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Button } from '~/components/ui/button';

// Mock ticket data
const pastTickets = [
  {
    id: 1,
    eventName: 'Theater Talks: Death of a Salesman',
    date: 'Oct 3, 2025',
    time: '7:00 PM GMT-4',
    location: 'Broadway Theater, NYC',
    ticketType: 'General Admission',
    seat: 'General Admission',
    price: '$35.00',
    qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=ticket-123456',
    eventImage: 'https://images.unsplash.com/photo-1611424564056-9815cae36b41',
    orderNumber: '**********',
    attendeeName: 'John Mobbin',
    organizer: 'MASC Hospitality Group',
    venue: 'Under the Arches',
    eventSummary:
      "LISTEN UP, NYC! Harlem is NYC's Foodie Kingdom! Uptown Night Market is back every Second Thursday of the month.",
  },
];

const upcomingTickets = [
  {
    id: 2,
    eventName: 'Uptown Night Market',
    date: 'Oct 13, 2025',
    time: '4:00 PM GMT-4',
    endTime: '10:00 PM GMT-4',
    location: 'Under the Arches',
    ticketType: 'General Admission',
    seat: 'General Admission 4-10pm',
    price: '$15.00',
    qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=250x250&data=ticket-234567',
    eventImage: 'https://images.unsplash.com/photo-1563924595624-b73b73c5afd8',
    orderNumber: '4612238509',
    attendeeName: 'John Mobbin',
    organizer: 'MASC Hospitality Group',
    venue: 'Under the Arches',
    eventSummary:
      "LISTEN UP, NYC! Harlem is NYC's Foodie Kingdom! Uptown Night Market is back every Second Thursday of the month.",
  },
];

export default function TicketsScreen() {
  const navigation = useNavigation();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [activeTab, setActiveTab] = useState('upcoming');
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [bottomSheetIndex, setBottomSheetIndex] = useState(-1);

  // Bottom sheet reference and snap points
  const ticketBottomSheetRef = useRef<BottomSheet>(null);
  const snapPoints = useMemo(() => ['90%'], []);

  const screenWidth = Dimensions.get('window').width;

  const handleViewTicket = useCallback((ticket) => {
    setSelectedTicket(ticket);
    // Set the index to 0 to show the sheet
    setBottomSheetIndex(0);
  }, []);

  // Change back to hidden when closed
  const handleSheetChanges = useCallback((index: number) => {
    setBottomSheetIndex(index);
  }, []);

  const renderTicketBottomSheet = () => {
    if (!selectedTicket) return null;

    return (
      <BottomSheet
        ref={ticketBottomSheetRef}
        index={bottomSheetIndex}
        snapPoints={snapPoints}
        enablePanDownToClose
        onChange={handleSheetChanges}
        backdropComponent={RenderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <BottomSheetScrollView style={{ flex: 1 }}>
          <View className="flex-row items-center justify-between p-4">
            <TouchableOpacity onPress={() => setBottomSheetIndex(-1)}>
              <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
            </TouchableOpacity>
            <Text className={`font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
              My Tickets
            </Text>
            <View style={{ width: 24 }} />
          </View>

          <ScrollView className="flex-1">
            <View className="items-center justify-center bg-indigo-500/10 p-8">
              <Image
                source={{ uri: selectedTicket.eventImage }}
                className="h-24 w-full object-cover opacity-30"
                style={{ position: 'absolute', top: 0, left: 0, right: 0 }}
              />
              <View className="items-center justify-center">
                <Text className={`font-bold text-2xl ${isDark ? 'text-white' : 'text-indigo-900'}`}>
                  {selectedTicket.eventName}
                </Text>
              </View>
            </View>

            <View className="w-full items-center p-4">
              <View
                className="mb-4 w-full items-center rounded-lg p-4"
                style={{ backgroundColor: colors.grey5 }}>
                <Image
                  source={{ uri: selectedTicket.qrCode }}
                  className="mb-6 h-52 w-52"
                  style={{ backgroundColor: 'white' }}
                />

                <View className="w-full border-t border-dashed border-gray-300 pt-4 dark:border-gray-700">
                  <Text className="mb-2 text-gray-500 dark:text-gray-400">Name</Text>
                  <Text
                    className={`font-bold text-2xl ${isDark ? 'text-white' : 'text-indigo-900'} mb-6`}>
                    {selectedTicket.attendeeName}
                  </Text>

                  <Text className="mb-2 text-gray-500 dark:text-gray-400">Event</Text>
                  <Text
                    className={`font-bold text-xl ${isDark ? 'text-white' : 'text-indigo-900'} mb-6`}>
                    {selectedTicket.eventName}
                  </Text>

                  <Text className="mb-2 text-gray-500 dark:text-gray-400">Ticket/seat</Text>
                  <Text
                    className={`font-bold text-lg ${isDark ? 'text-white' : 'text-indigo-900'} mb-6`}>
                    {selectedTicket.seat}
                  </Text>

                  <View className="mb-6 flex-row">
                    <View className="flex-1">
                      <Text className="mb-2 text-gray-500 dark:text-gray-400">Date</Text>
                      <Text className={`text-base ${isDark ? 'text-white' : 'text-indigo-900'}`}>
                        {selectedTicket.date}
                      </Text>
                      <Text className={`text-base ${isDark ? 'text-white' : 'text-indigo-900'}`}>
                        {selectedTicket.time}{' '}
                        {selectedTicket.endTime ? `— ${selectedTicket.endTime}` : ''}
                      </Text>
                    </View>

                    <View className="flex-1">
                      <Text className="mb-2 text-gray-500 dark:text-gray-400">Venue</Text>
                      <Text className={`text-base ${isDark ? 'text-white' : 'text-indigo-900'}`}>
                        {selectedTicket.venue}
                      </Text>
                      <TouchableOpacity>
                        <Text className="text-blue-500">View Map</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </View>

              <View className="mb-5 w-full">
                <Text className="mb-2 text-gray-500 dark:text-gray-400">Order number</Text>
                <Text className={`text-base ${isDark ? 'text-white' : 'text-black'}`}>
                  #{selectedTicket.orderNumber}
                </Text>
              </View>

              <View className="mb-5 w-full">
                <Text className="mb-2 text-gray-500 dark:text-gray-400">Event summary</Text>
                <Text className={`text-base ${isDark ? 'text-white' : 'text-black'}`}>
                  {selectedTicket.eventSummary}
                </Text>
                <TouchableOpacity className="mt-2">
                  <Text className="text-blue-500">View event listing</Text>
                </TouchableOpacity>
              </View>

              <View className="mb-5 w-full">
                <Text className="mb-2 text-gray-500 dark:text-gray-400">Organiser</Text>
                <View className="flex-row items-center justify-between">
                  <Text className={`text-base ${isDark ? 'text-white' : 'text-black'}`}>
                    {selectedTicket.organizer}
                  </Text>
                </View>
              </View>

              <Button
                size="lg"
                variant="solid"
                className={`mt-4 h-14 rounded-xl ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
                onPress={() => {}}>
                <Text className="font-bold text-white">Save ticket as image</Text>
              </Button>
            </View>
          </ScrollView>
        </BottomSheetScrollView>
      </BottomSheet>
    );
  };

  const renderTickets = () => {
    const currentTickets = activeTab === 'upcoming' ? upcomingTickets : pastTickets;

    if (currentTickets.length === 0) {
      return (
        <View className="mt-24 items-center justify-center">
          <View className="mb-5 h-24 w-24 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800">
            <MaterialCommunityIcons
              name="ticket-confirmation-outline"
              size={40}
              color={isDark ? '#aaa' : '#ccc'}
            />
          </View>
          <Text
            className={`mb-5 text-center font-medium text-xl ${isDark ? 'text-white' : 'text-gray-800'}`}>
            No tickets yet
          </Text>
          <Text
            className={`mb-7 text-center text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            Make sure you're in the same account that purchased your tickets
          </Text>
          <TouchableOpacity className="rounded-lg bg-gray-200 px-10 py-3 dark:bg-gray-700">
            <Text className={`font-medium text-base ${isDark ? 'text-white' : 'text-gray-800'}`}>
              Try again
            </Text>
          </TouchableOpacity>

          <TouchableOpacity className="mt-10">
            <Text className="text-center text-blue-500">Visit our help centre</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return currentTickets.map((ticket, index) => (
      <TouchableOpacity
        key={ticket.id}
        className="mt-5 flex-row items-center border-b border-gray-200 pb-4 dark:border-gray-700"
        onPress={() => handleViewTicket(ticket)}>
        <View className="relative mr-4 h-16 w-16 overflow-hidden rounded-md">
          <View className="absolute left-0 top-0 z-10 flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700">
            <Text className={`font-medium text-sm ${isDark ? 'text-white' : 'text-gray-800'}`}>
              {index + 1}
            </Text>
          </View>
          <Image source={{ uri: ticket.eventImage }} className="h-full w-full" />
        </View>

        <View className="flex-1">
          <Text className={`mb-1 font-bold text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            {ticket.eventName}
          </Text>
          <Text className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            {ticket.date} • {ticket.time}
          </Text>
        </View>

        <View>
          <Text className="text-blue-500">View ticket</Text>
        </View>
      </TouchableOpacity>
    ));
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View className="flex-1" style={{ backgroundColor: colors.background }}>
        <StatusBar style={isDark ? 'light' : 'dark'} />

        {/* Header with Back Button */}
        <View className="flex-row items-center justify-between px-4 pb-2 pt-12">
          <TouchableOpacity onPress={() => navigation.goBack()} className="p-1">
            <Ionicons name="arrow-back" size={24} color={isDark ? '#fff' : '#000'} />
          </TouchableOpacity>
          <Text className={`font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
            Tickets
          </Text>
          <View style={{ width: 24 }} />
        </View>

        {/* Tabs */}
        <View className="flex-row border-b border-gray-200 dark:border-gray-800">
          <TouchableOpacity
            className={`flex-1 items-center justify-center py-4 ${activeTab === 'upcoming' ? 'border-b-2 border-blue-500' : ''}`}
            onPress={() => setActiveTab('upcoming')}>
            <Text
              className={`font-medium ${
                activeTab === 'upcoming'
                  ? 'text-blue-500'
                  : isDark
                    ? 'text-gray-400'
                    : 'text-gray-500'
              }`}>
              Upcoming
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className={`flex-1 items-center justify-center py-4 ${activeTab === 'past' ? 'border-b-2 border-blue-500' : ''}`}
            onPress={() => setActiveTab('past')}>
            <Text
              className={`font-medium ${
                activeTab === 'past' ? 'text-blue-500' : isDark ? 'text-gray-400' : 'text-gray-500'
              }`}>
              Past tickets
            </Text>
          </TouchableOpacity>
        </View>

        {/* Ticket List */}
        <ScrollView className="flex-1 px-4">
          {activeTab === 'upcoming' && <View>{renderTickets()}</View>}

          {activeTab === 'past' && renderTickets()}

          {activeTab === 'upcoming' && upcomingTickets.length === 0 && (
            <View className="mt-24 items-center justify-center">
              <View className="mb-5 h-24 w-24 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800">
                <MaterialCommunityIcons
                  name="ticket-confirmation-outline"
                  size={40}
                  color={isDark ? '#aaa' : '#ccc'}
                />
              </View>
              <Text
                className={`mb-5 text-center font-medium text-xl ${isDark ? 'text-white' : 'text-gray-800'}`}>
                No tickets yet
              </Text>
              <Text
                className={`mb-7 text-center text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                Make sure you're in the same account that purchased your tickets
              </Text>
              <TouchableOpacity className="rounded-lg bg-gray-200 px-10 py-3 dark:bg-gray-700">
                <Text
                  className={`font-medium text-base ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  Try again
                </Text>
              </TouchableOpacity>

              <TouchableOpacity className="mt-10">
                <Text className="text-center text-blue-500">Visit our help centre</Text>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>

        {/* Ticket Detail Bottom Sheet */}
        {renderTicketBottomSheet()}
      </View>
    </GestureHandlerRootView>
  );
}
