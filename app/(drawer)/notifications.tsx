import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

const Notifications = () => {
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();

  const notifications = [
    { id: 1, type: 'event', message: 'Your event "Summer Bash" has been created successfully.' },
    { id: 2, type: 'payment', message: 'Payment for "Music Fest" has been processed.' },
    { id: 3, type: 'chat', message: 'You have a new message from <PERSON>.' },
    { id: 4, type: 'friend_request', message: '<PERSON> sent you a friend request.' },
    { id: 5, type: 'event', message: 'Reminder: "Tech Conference" starts tomorrow.' },
  ];

  return (
    <View className={`flex-1 px-4 pt-12`} style={{ backgroundColor: colors.background }}>
      <View className="flex-row items-center justify-between pb-4">
        <TouchableOpacity onPress={() => router.back()} className="p-2">
          <Ionicons name="arrow-back" size={24} color={isDark ? '#fff' : '#000'} />
        </TouchableOpacity>
        <Text className={`font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
          Notifications
        </Text>
        <View className="p-2" />
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 40 }}>
        {notifications.map((notification) => (
          <View
            key={notification.id}
            className={`mb-4 flex-row overflow-hidden rounded-xl px-4 py-3 shadow-sm`}
            style={{ backgroundColor: colors.grey5 }}>
            <MaterialCommunityIcons
              name={
                notification.type === 'event'
                  ? 'calendar'
                  : notification.type === 'payment'
                    ? 'credit-card'
                    : notification.type === 'chat'
                      ? 'message'
                      : 'account-plus'
              }
              size={24}
              color={isDark ? '#fff' : '#000'}
              style={{ marginRight: 10 }}
            />
            <Text className={`flex-1 text-base ${isDark ? 'text-white' : 'text-black'}`}>
              {notification.message}
            </Text>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default Notifications;
