import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  FlatList,
  TextInput,
  ScrollView,
  Platform,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useFocusEffect, useNavigation, useRouter } from 'expo-router';
import { Ionicons, MaterialIcons, FontAwesome } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import { Friend } from '~/types/chat_type';
import { FriendService } from '~/services/FriendService';
import { UserStore } from '~/store/store';
import { Toast } from 'toastify-react-native';

export default function FriendsScreen() {
  const navigation = useNavigation();
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const user = UserStore((state: any) => state.user);

  const [searchQuery, setSearchQuery] = useState('');
  const [friends, setFriends] = useState<(Friend & { hasStory?: boolean })[]>([]);
  const [filteredFriends, setFilteredFriends] = useState<(Friend & { hasStory?: boolean })[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch friends on component mount
  useFocusEffect(
    useCallback(() => {
      fetchFriends();
    }, [user?.id])
  );

  const fetchFriends = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const response = await FriendService.getFriends(user.id);
      if (response.success && response.body) {
        // Transform the response to match our Friend interface
        const friendsData = response.body.friends.map((friend: any) => {
          // Determine online status based on loginTime
          let isOnline = false;
          if (friend.loginTime) {
            const loginTime = new Date(friend.loginTime);
            const currentTime = new Date();
            const timeDifferenceMinutes =
              (currentTime.getTime() - loginTime.getTime()) / (1000 * 60);

            if (timeDifferenceMinutes <= 6) {
              isOnline = true;
            }
          }

          return {
            id: friend.id,
            email: friend.email,
            name: friend.fullName || friend.name,
            avatar:
              friend.profilePicture?.[friend.profilePicture.length - 1]?.secureUrl ||
              friend.profilePhoto ||
              'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
            status: isOnline ? 'online' : 'offline',
            hasStory: false, // You can implement story checking logic here
          };
        });
        setFriends(friendsData);
        setFilteredFriends(friendsData);
      }
    } catch (error) {
      console.error('Error fetching friends:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load friends',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchFriends();
    setRefreshing(false);
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text.trim() === '') {
      setFilteredFriends(friends);
    } else {
      const filtered = friends.filter((friend) =>
        friend.name.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredFriends(filtered);
    }
  };

  const handleViewProfile = (friend: Friend) => {
    // Navigate to profile view

    router.push({
      pathname: '/Auth/viewProfile',
      params: { userId: friend.id || '', email: friend.email.toString() },
    });
  };

  const handleChat = (friend: Friend) => {
    // Navigate to chat
    router.push({
      pathname: '/chat',
      params: { userId: friend.id, name: friend.name },
    });
  };

  const handleViewStory = (friend: Friend) => {
    // Navigate to story view
    router.push({
      pathname: '/story/view',
      params: { userId: friend.id },
    });
  };

  const renderFriendItem = ({ item }: { item: Friend & { hasStory?: boolean } }) => {
    return (
      <View
        className="flex-row items-center px-4 py-4 border-b"
        style={{ borderBottomColor: colors.grey5 }}>
        <TouchableOpacity
          className="relative"
          onPress={() => item.hasStory && handleViewStory(item)}>
          <View
            className={`${item.hasStory ? 'border-2 border-violet-600 p-0.5' : ''} rounded-full`}>
            <Image source={{ uri: item.avatar }} className="rounded-full h-14 w-14" />
          </View>
          <View
            className={`absolute bottom-0 right-0 h-3.5 w-3.5 rounded-full border border-white ${item.status === 'online' ? 'bg-green-500' : 'bg-gray-400'}`}
          />
        </TouchableOpacity>
        <View className="flex-1 ml-3">
          <Text
            className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}
            numberOfLines={1}>
            {item.name}
          </Text>
          <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            {item.status === 'online' ? 'Online' : 'Offline'}
          </Text>
        </View>
        <View className="flex-row">
          <TouchableOpacity
            className="items-center justify-center w-10 h-10 mr-3 rounded-full"
            style={{ backgroundColor: colors.grey5 }}
            onPress={() => handleChat(item)}>
            <Ionicons
              name="chatbubble-outline"
              size={20}
              color={isDark ? colors.foreground : colors.foreground}
            />
          </TouchableOpacity>
          <TouchableOpacity
            className="items-center justify-center w-10 h-10 rounded-full"
            style={{ backgroundColor: colors.grey5 }}
            onPress={() => handleViewProfile(item)}>
            <Ionicons
              name="person-outline"
              size={20}
              color={isDark ? colors.foreground : colors.foreground}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <View className="flex-1" style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View className="flex-row items-center justify-between px-4 pt-12 pb-4">
        <TouchableOpacity className="p-2" onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={isDark ? '#fff' : '#000'} />
        </TouchableOpacity>
        <Text className={`font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
          Friends
        </Text>
        <Text></Text>
      </View>

      {/* Friend Request Navigation */}
      <View className="flex-row justify-around px-4 mb-4">
        <TouchableOpacity
          className="flex-1 py-3 mr-2 rounded-lg"
          style={{ backgroundColor: colors.grey5 }}
          onPress={() => router.push('/friends/received-requests')}>
          <Text className={`text-center font-medium ${isDark ? 'text-white' : 'text-black'}`}>
            Received Requests
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className="flex-1 py-3 ml-2 rounded-lg"
          style={{ backgroundColor: colors.grey5 }}
          onPress={() => router.push('/friends/sent-requests')}>
          <Text className={`text-center font-medium ${isDark ? 'text-white' : 'text-black'}`}>
            Sent Requests
          </Text>
        </TouchableOpacity>
      </View>

      <View className="px-4 mb-4">
        <View
          className="flex-row items-center px-3 py-2 rounded-xl"
          style={{ backgroundColor: colors.grey5 }}>
          <Ionicons name="search-outline" size={20} color={isDark ? colors.grey2 : colors.grey2} />
          <TextInput
            className={`ml-2 flex-1 ${isDark ? 'text-white' : 'text-black'}`}
            placeholder="Search friends"
            placeholderTextColor={isDark ? colors.grey2 : colors.grey2}
            value={searchQuery}
            onChangeText={handleSearch}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => handleSearch('')}>
              <Ionicons
                name="close-circle"
                size={20}
                color={isDark ? colors.grey2 : colors.grey2}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {loading ? (
        <View className="items-center justify-center flex-1">
          <ActivityIndicator size="large" color={colors.primary} />
          <Text className={`mt-4 ${isDark ? 'text-white' : 'text-black'}`}>Loading friends...</Text>
        </View>
      ) : filteredFriends.length > 0 ? (
        <FlatList
          data={filteredFriends}
          renderItem={renderFriendItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={{ paddingBottom: Platform.OS === 'android' ? 100 : 40 }}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={10}
          scrollEventThrottle={16}
          overScrollMode="never"
          bounces={false}
          refreshing={refreshing}
          onRefresh={handleRefresh}
        />
      ) : (
        <View className="items-center justify-center flex-1 px-4">
          <Ionicons name="people-outline" size={60} color={isDark ? colors.grey3 : colors.grey4} />
          <Text
            className={`mt-4 text-center font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            No Friends Found
          </Text>
          <Text className={`mt-2 text-center ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            {searchQuery ? `No results for "${searchQuery}"` : "You don't have any friends yet"}
          </Text>
        </View>
      )}
    </View>
  );
}
