import * as Location from 'expo-location';
import * as SecureStore from 'expo-secure-store';

import { AuthService } from '~/services/AuthService';
import { UserService } from '~/services/UserService';
import { AuthStore, UserStore } from '~/store/store';

// JWT token utility functions
export function isTokenExpired(token: string): boolean {
  if (!token) return true;

  try {
    // Decode JWT payload (base64 decode the middle part)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Date.now() / 1000; // Convert to seconds

    // Check if token has expired (with 5 minute buffer)
    return payload.exp && payload.exp < currentTime + 300;
  } catch {
    // If we can't parse the token, consider it expired
    return true;
  }
}

export function isTokenValid(token: string): boolean {
  if (!token) return false;

  try {
    // Basic JWT structure validation
    const parts = token.split('.');
    if (parts.length !== 3) return false;

    // Try to decode payload
    const payload = JSON.parse(atob(parts[1]));
    return !!payload.exp; // Token should have expiration
  } catch {
    return false;
  }
}

// Check if user has granted required permissions
export async function checkPermissions(): Promise<boolean> {
  try {
    const locationPermission = await Location.getForegroundPermissionsAsync();

    const hasPermissions = locationPermission.status === 'granted';

    if (hasPermissions) {
      // Permissions ARE granted - enable permissions in store
      (AuthStore.getState() as { enable: () => void }).enable();
    } else {
      // Permissions NOT granted - disable permissions in store
      (AuthStore.getState() as { disable: () => void }).disable();
    }

    return hasPermissions;
  } catch (error) {
    console.error('Error checking permissions:', error);
    return false;
  }
}

// Attempt to refresh the access token using refresh token
export async function refreshAccessToken(): Promise<boolean> {
  try {
    const refreshToken = await SecureStore.getItemAsync('refreshToken');
    if (!refreshToken || isTokenExpired(refreshToken)) {
      return false;
    }

    const response = await AuthService.refreshToken(refreshToken);
    if (response.success && response.body?.accessToken) {
      // Store new access token
      await SecureStore.setItemAsync('accessToken', response.body.accessToken);

      // Update user data if provided
      if (response.body.refreshToken) {
        await SecureStore.setItemAsync('refreshToken', response.body.refreshToken);
      }

      return true;
    }

    return false;
  } catch (error) {
    console.error('Error refreshing token:', error);
    return false;
  }
}

// Load user profile and update store
export async function loadUserProfile(): Promise<boolean> {
  try {
    const profileData = await UserService.getMyProfile();
    if (profileData.success) {
      (UserStore.getState() as { setUser: (data: any) => void }).setUser(profileData.body);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error loading user profile:', error);
    return false;
  }
}
