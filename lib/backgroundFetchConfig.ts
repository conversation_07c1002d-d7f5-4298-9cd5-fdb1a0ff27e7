/**
 * Background Fetch Configuration
 *
 * Centralized configuration for the background data fetching system.
 * Modify these values to customize fetch behavior across the app.
 */

export interface FetchConfig {
  // Interval configurations (in milliseconds)
  intervals: {
    events: number;
    people: number;
    categories: number;
  };

  // Performance thresholds
  performance: {
    minInterval: number; // Minimum time between fetches of same type
    maxRetryInterval: number; // Maximum interval when retrying failed requests
    successRateThreshold: number; // Success rate threshold for interval optimization
    fastFetchThreshold: number; // Fast fetch duration threshold for optimization
  };

  // Feature flags
  features: {
    appStateAwareness: boolean; // Pause fetching when app is backgrounded
    networkOptimization: boolean; // Check network conditions before fetching
    performanceMetrics: boolean; // Track and optimize based on performance
    dynamicIntervals: boolean; // Adjust intervals based on success rates
  };

  // Development/debugging
  debug: {
    enableLogs: boolean; // Enable detailed console logging
    enableMetrics: boolean; // Enable performance metrics logging
  };
}

// Default configuration
export const DEFAULT_FETCH_CONFIG: FetchConfig = {
  intervals: {
    events: 5 * 60 * 1000, // 5 minutes - events change moderately
    people: 3 * 60 * 1000, // 3 minutes - people data is most dynamic (location, status)
    categories: 30 * 60 * 1000, // 30 minutes - categories rarely change
  },

  performance: {
    minInterval: 30 * 1000, // 30 seconds minimum between same-type fetches
    maxRetryInterval: 30 * 60 * 1000, // 30 minutes max when backing off
    successRateThreshold: 0.7, // 70% success rate threshold
    fastFetchThreshold: 1000, // 1 second threshold for "fast" fetches
  },

  features: {
    appStateAwareness: true, // Pause when app is backgrounded
    networkOptimization: true, // Check network before fetching
    performanceMetrics: true, // Track performance
    dynamicIntervals: true, // Adjust intervals based on performance
  },

  debug: {
    enableLogs: __DEV__, // Enable logs in development only
    enableMetrics: __DEV__, // Enable metrics in development only
  },
};

// Environment-specific configurations
export const DEVELOPMENT_CONFIG: Partial<FetchConfig> = {
  intervals: {
    events: 2 * 60 * 1000, // 2 minutes - faster for development
    people: 1 * 60 * 1000, // 1 minute - faster for development
    categories: 10 * 60 * 1000, // 10 minutes - faster for development
  },
  debug: {
    enableLogs: true,
    enableMetrics: true,
  },
};

export const PRODUCTION_CONFIG: Partial<FetchConfig> = {
  intervals: {
    events: 10 * 60 * 1000, // 10 minutes - slower for production to save data
    people: 5 * 60 * 1000, // 5 minutes - slower for production
    categories: 60 * 60 * 1000, // 1 hour - slower for production
  },
  debug: {
    enableLogs: false,
    enableMetrics: false,
  },
};

// Network-specific configurations
export const WIFI_CONFIG: Partial<FetchConfig> = {
  intervals: {
    events: 3 * 60 * 1000, // 3 minutes on WiFi
    people: 2 * 60 * 1000, // 2 minutes on WiFi
    categories: 20 * 60 * 1000, // 20 minutes on WiFi
  },
};

export const CELLULAR_CONFIG: Partial<FetchConfig> = {
  intervals: {
    events: 10 * 60 * 1000, // 10 minutes on cellular
    people: 5 * 60 * 1000, // 5 minutes on cellular
    categories: 60 * 60 * 1000, // 1 hour on cellular
  },
};

// Low data mode configuration
export const LOW_DATA_CONFIG: Partial<FetchConfig> = {
  intervals: {
    events: 30 * 60 * 1000, // 30 minutes in low data mode
    people: 15 * 60 * 1000, // 15 minutes in low data mode
    categories: 2 * 60 * 60 * 1000, // 2 hours in low data mode
  },
  features: {
    networkOptimization: true,
    dynamicIntervals: false, // Disable dynamic adjustments to save data
  },
};

/**
 * Utility function to merge configurations
 */
export function mergeConfigs(...configs: Partial<FetchConfig>[]): FetchConfig {
  return configs.reduce((merged, config) => {
    return {
      ...merged,
      ...config,
      intervals: { ...merged.intervals, ...config.intervals },
      performance: { ...merged.performance, ...config.performance },
      features: { ...merged.features, ...config.features },
      debug: { ...merged.debug, ...config.debug },
    };
  }, DEFAULT_FETCH_CONFIG);
}

/**
 * Get configuration based on environment and network conditions
 */
export function getOptimalConfig(
  environment: 'development' | 'production' = __DEV__ ? 'development' : 'production',
  networkType?: 'wifi' | 'cellular' | 'unknown',
  lowDataMode: boolean = false
): FetchConfig {
  let configs: Partial<FetchConfig>[] = [DEFAULT_FETCH_CONFIG];

  // Environment config
  if (environment === 'development') {
    configs.push(DEVELOPMENT_CONFIG);
  } else {
    configs.push(PRODUCTION_CONFIG);
  }

  // Network config
  if (networkType === 'wifi') {
    configs.push(WIFI_CONFIG);
  } else if (networkType === 'cellular') {
    configs.push(CELLULAR_CONFIG);
  }

  // Low data mode
  if (lowDataMode) {
    configs.push(LOW_DATA_CONFIG);
  }

  return mergeConfigs(...configs);
}

// Example usage:
// const config = getOptimalConfig('production', 'cellular', false);
// const devConfig = getOptimalConfig('development', 'wifi', false);
// const lowDataConfig = getOptimalConfig('production', 'cellular', true);
