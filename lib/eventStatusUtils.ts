export interface EventStatusResult {
  status: 'In Progress' | 'Ended' | null;
  backgroundColor: string;
}

/**
 * Determines the status of an event based on its start and end times
 * @param startDateTime - Event start date/time string
 * @param endDateTime - Event end date/time string (optional, defaults to startDateTime)
 * @returns EventStatusResult with status and background color, or null if event hasn't started
 */
export const getEventStatus = (startDateTime?: string, endDateTime?: string): EventStatusResult => {
  if (!startDateTime) {
    return { status: null, backgroundColor: '' };
  }

  const now = new Date();
  const eventStart = new Date(startDateTime);
  const eventEnd = new Date(endDateTime || startDateTime);

  if (now >= eventStart && now <= eventEnd) {
    return {
      status: 'In Progress',
      backgroundColor: '#10b981', // green
    };
  } else if (now > eventEnd) {
    return {
      status: 'Ended',
      backgroundColor: '#ef4444', // red
    };
  }

  return { status: null, backgroundColor: '' };
};

/**
 * React component for displaying event status chip
 * @param startDateTime - Event start date/time string
 * @param endDateTime - Event end date/time string (optional)
 * @param className - Additional CSS classes for the container
 * @param size - Size variant for the chip ('sm' | 'md' | 'lg')
 */
export const EventStatusChip: React.FC<{
  startDateTime?: string;
  endDateTime?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}> = ({ startDateTime, endDateTime, className = '', size = 'md' }) => {
  const React = require('react');
  const { View, Text } = require('react-native');

  const eventStatus = getEventStatus(startDateTime, endDateTime);

  if (!eventStatus.status) return null;

  const sizeClasses = {
    sm: 'px-1.5 py-0.5',
    md: 'px-2 py-1',
    lg: 'px-3 py-1.5',
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-xs',
    lg: 'text-sm',
  };

  return React.createElement(
    View,
    {
      className: `absolute rounded-full ${sizeClasses[size]} ${className}`,
      style: { backgroundColor: eventStatus.backgroundColor },
    },
    React.createElement(
      Text,
      {
        className: `font-bold ${textSizeClasses[size]} text-white`,
      },
      eventStatus.status
    )
  );
};
