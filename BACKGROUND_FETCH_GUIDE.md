# Continuous Non-Blocking Data Fetching Implementation

## Overview

This implementation provides a robust, performance-optimized background data fetching system for your React Native social events app. The system continuously fetches users and events data without affecting app performance or user experience.

## Features

### ✅ Core Features

- **Non-blocking background fetching** - Runs in background without blocking UI
- **App state awareness** - Pauses when app is inactive to save battery/data
- **Performance monitoring** - Tracks success rates and fetch durations
- **Smart intervals** - Different intervals for different data types based on update frequency
- **Error handling** - Graceful error handling that doesn't break the app
- **Request deduplication** - Prevents multiple requests in short time periods
- **Network optimization** - Can be extended with network condition checks
- **User control** - Enable/disable continuous fetching on demand

### 🎯 Performance Optimizations

1. **Dynamic Intervals**: Optimized for real-time updates during active app usage

   - Events: 30 seconds (real-time event updates)
   - People: 20 seconds (location updates, status changes)
   - Categories: 5 minutes (rarely change)

2. **Smart Fetch Logic**:

   - Minimum 10-second intervals between same-type requests
   - App state monitoring (pauses when app is backgrounded)
   - Network condition checks (extensible)
   - Performance metrics tracking
   - User-controlled enable/disable functionality

3. **Memory Efficiency**:
   - Proper cleanup of intervals and listeners
   - Memoized callbacks and context values
   - Selective re-renders using context selectors

## Quick Start

### Basic Usage

```tsx
import { useEvent } from '~/providers/MapProvider';

function MyComponent() {
  const {
    isContinuousFetchEnabled,
    setIsContinuousFetchEnabled,
    isLoadingEvents,
    isLoadingPeople,
  } = useEvent();

  // Toggle continuous fetching
  const toggleFetching = () => {
    setIsContinuousFetchEnabled(!isContinuousFetchEnabled);
  };

  return (
    <View>
      <Text>Continuous Fetching: {isContinuousFetchEnabled ? 'ON' : 'OFF'}</Text>
      <Button onPress={toggleFetching}>
        {isContinuousFetchEnabled ? 'Disable' : 'Enable'} Continuous Fetch
      </Button>
    </View>
  );
}
```

### Control Interface

Use the provided `ContinuousFetchDemo` component to add user controls:

```tsx
import ContinuousFetchDemo from '~/components/Demo/ContinuousFetchDemo';

function SettingsScreen() {
  return (
    <View>
      <ContinuousFetchDemo />
    </View>
  );
}
```

## Implementation Details

### 1. Background Fetch Manager

```typescript
const BackgroundFetchManager = {
  // Tracks performance metrics for each data type
  metrics: {
    events: { successCount: 0, errorCount: 0, avgDuration: 0 },
    people: { successCount: 0, errorCount: 0, avgDuration: 0 },
    categories: { successCount: 0, errorCount: 0, avgDuration: 0 },
  },

  // Automatically adjusts intervals based on success rates
  getOptimizedInterval: (baseInterval: number, type: string) => { ... },

  // Records fetch performance for optimization
  recordFetch: (type: string, success: boolean, duration: number) => { ... },

  // Network condition checking (extensible)
  isNetworkOptimal: () => { ... }
}
```

### 2. Background Fetch Implementation

```typescript
React.useEffect(() => {
  // Configuration
  const FETCH_INTERVALS = {
    events: 5 * 60 * 1000, // 5 minutes
    people: 3 * 60 * 1000, // 3 minutes
    categories: 30 * 60 * 1000, // 30 minutes
  };

  // Enhanced fetch function with optimizations
  const createBackgroundFetch = (fetchFunction, interval, name) => {
    return setInterval(async () => {
      // Multiple checks before fetching:
      // - App state (is active?)
      // - Time since last fetch (minimum interval)
      // - Network conditions

      if (shouldSkipFetch()) return;

      try {
        const startTime = Date.now();
        await fetchFunction();
        const duration = Date.now() - startTime;
        BackgroundFetchManager.recordFetch(name, true, duration);
      } catch (error) {
        BackgroundFetchManager.recordFetch(name, false, 0);
        // Don't throw - keep app stable
      }
    }, interval);
  };

  // Cleanup function clears all intervals and listeners
  return () => {
    /* cleanup */
  };
}, [fetchEvents, fetchPeople, fetchCategories]);
```

## Usage

### Accessing Data and Loading States

```tsx
import { useEvent, useEventValue } from '~/providers/MapProvider';

function MyComponent() {
  // Get all context values
  const { People, filteredEvents, categories } = useEvent();

  // Or get specific values to prevent unnecessary re-renders
  const isLoadingPeople = useEventValue('isLoadingPeople');
  const peopleError = useEventValue('peopleError');

  // Manual fetch if needed (will respect minimum intervals)
  const { fetchPeople, fetchEvents } = useEvent();

  const handleRefresh = () => {
    fetchPeople(); // Will only fetch if minimum interval has passed
  };
}
```

### Loading States and Error Handling

```tsx
function DataComponent() {
  const { isLoadingEvents, isLoadingPeople, eventsError, peopleError } = useEvent();

  if (isLoadingEvents && !events.length) {
    return <LoadingSpinner />;
  }

  if (eventsError) {
    return <ErrorMessage error={eventsError} onRetry={fetchEvents} />;
  }

  return <YourDataComponent />;
}
```

## Configuration Options

### Customizing Intervals

To modify fetch intervals, update the `FETCH_INTERVALS` object in the `MapProvider`:

```typescript
const FETCH_INTERVALS = {
  events: 2 * 60 * 1000, // 2 minutes (more frequent)
  people: 1 * 60 * 1000, // 1 minute (very frequent)
  categories: 60 * 60 * 1000, // 1 hour (less frequent)
};
```

### Network Optimization

Extend the `isNetworkOptimal` method to include network condition checks:

```typescript
// Install: npm install @react-native-netinfo/netinfo
import NetInfo from '@react-native-netinfo/netinfo';

isNetworkOptimal: async () => {
  const state = await NetInfo.fetch();

  // Only fetch on WiFi or good cellular
  if (state.type === 'wifi') return true;
  if (state.type === 'cellular' && state.details.cellularGeneration === '4g') return true;

  return false;
};
```

## Monitoring and Debugging

### Performance Metrics

```typescript
// Access performance metrics for debugging
console.log('Events fetch metrics:', BackgroundFetchManager.metrics.events);
console.log('People fetch metrics:', BackgroundFetchManager.metrics.people);
```

### Debug Logs

The system provides detailed console logs:

- `🔄 Background fetching [type]...` - Fetch started
- `✅ Background fetch [type] completed in [time]ms` - Fetch succeeded
- `❌ Background fetch [type] failed` - Fetch failed
- `⏸️ Skipping [type] fetch - [reason]` - Fetch skipped (with reason)
- `🧹 Cleaning up background fetch intervals` - Cleanup on unmount

## Best Practices

### 1. **Component-Level Optimization**

```tsx
// Use selective context access to prevent unnecessary re-renders
const people = useEventValue('People'); // ✅ Good
const { People } = useEvent(); // ❌ Causes more re-renders
```

### 2. **Error Boundary Integration**

```tsx
function AppWithErrorBoundary() {
  return (
    <ErrorBoundary fallback={<ErrorScreen />}>
      <EventProvider>
        <App />
      </EventProvider>
    </ErrorBoundary>
  );
}
```

### 3. **Loading State Management**

```tsx
// Handle loading states gracefully
const { People, isLoadingPeople } = useEvent();

// Show skeleton while initial load
if (isLoadingPeople && People.length === 0) {
  return <PeopleSkeleton />;
}

// Show current data + subtle loading indicator for background updates
return (
  <View>
    <PeopleList data={People} />
    {isLoadingPeople && <BackgroundRefreshIndicator />}
  </View>
);
```

## Troubleshooting

### Common Issues

1. **High Memory Usage**:

   - Check that cleanup functions are working properly
   - Monitor interval cleanup in component unmount

2. **Too Many API Calls**:

   - Verify minimum interval enforcement
   - Check app state monitoring is working

3. **Stale Data**:
   - Ensure fetch functions are properly memoized
   - Check interval configurations

### Performance Tips

1. **Use `useEventValue` for specific values** to prevent unnecessary re-renders
2. **Implement error boundaries** around data-dependent components
3. **Monitor network usage** in production and adjust intervals accordingly
4. **Use React DevTools Profiler** to identify performance bottlenecks

## Future Enhancements

### Potential Improvements

1. **Smart Caching**: Implement cache invalidation strategies
2. **Network-Aware Intervals**: Adjust intervals based on connection type
3. **User Behavior Analytics**: Fetch more frequently when user is active
4. **Background Sync**: Use React Native Background Tasks for offline scenarios
5. **Real-time Updates**: Integrate WebSocket connections for instant updates

### Example Network-Aware Implementation

```typescript
import NetInfo from '@react-native-netinfo/netinfo';

// Enhanced network checking
isNetworkOptimal: async () => {
  const state = await NetInfo.fetch();

  if (!state.isConnected) return false;

  switch (state.type) {
    case 'wifi':
      return true;
    case 'cellular':
      return state.details.cellularGeneration === '4g' || state.details.cellularGeneration === '5g';
    default:
      return false;
  }
};
```

This implementation provides a solid foundation for continuous, non-blocking data fetching while maintaining excellent performance and user experience.
