# Social Events API Endpoints

/_ "postinstall": "patch-package" _/
This document outlines the API endpoints required for the Social Events application, including data structures, expected responses, and user flows.

## Table of Contents

1. [Authentication](#authentication)
2. [User Management](#user-management)
3. [Events](#events)
4. [Maps & Location](#maps--location)
5. [Social Features](#social-features)
6. [Notifications](#notifications)
7. [User Story](#user-story)

---

## Authentication

### POST /api/auth/register

Register a new user account.

**Request Body:**

```json
{
  "fullName": "Panashe Mushinyi",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "termsAccepted": true
}
```

**Response (201 Created):**

```json
{
  "success": true,
  "message": "Registration successful. Verification code sent to email.",
  "userId": "user-uuid-123"
}
```

### POST /api/auth/verify-email

Verify user's email with verification code.

**Request Body:**

```json
{
  "userId": "user-uuid-123",
  "verificationCode": "123456"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Email verified successfully",
  "nextStep": "profile-setup"
}
```

### POST /api/auth/login

Authenticate a user and get access token.

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "accessToken": "jwt-token-here",
  "refreshToken": "refresh-token-here",
  "user": {
    "id": "user-uuid-123",
    "fullName": "Panashe Mushinyi",
    "email": "<EMAIL>",
    "isSetupComplete": true,
    "hasPermissions": true
  }
}
```

### POST /api/auth/refresh-token

Refresh the access token using a refresh token.

**Request Body:**

```json
{
  "refreshToken": "refresh-token-here"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "accessToken": "new-jwt-token-here",
  "refreshToken": "new-refresh-token-here"
}
```

### POST /api/auth/forgot-password

Request a password reset.

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Password reset instructions sent to email"
}
```

### POST /api/auth/reset-password

Reset password with token received via email.

**Request Body:**

```json
{
  "token": "reset-token-from-email",
  "newPassword": "newSecurePassword123"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Password reset successful"
}
```

### POST /api/auth/logout

Invalidate the current session.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

---

## User Management

### POST /api/users/profile-setup

Complete user profile setup after registration.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "username": "panashe_m",
  "gender": "male",
  "interests": ["Technology", "Music", "Art", "Photography", "Travel"],
  "eventPreferences": ["Leisure", "Entertainment", "Education"]
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Profile setup completed",
  "user": {
    "id": "user-uuid-123",
    "username": "panashe_m",
    "isSetupComplete": true
  }
}
```

### GET /api/users/profile

Get the current user's profile.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "id": "user-uuid-123",
  "fullName": "Panashe Mushinyi",
  "username": "panashe_m",
  "email": "<EMAIL>",
  "bio": "Mobile developer passionate about creating seamless user experiences!",
  "profileImage": "https://example.com/profile-image.jpg",
  "location": "Harare, Zimbabwe",
  "stats": {
    "events": 15,
    "followers": 243,
    "following": 158,
    "attended": 27
  },
  "interests": ["Technology", "Music", "Art", "Photography", "Travel"],
  "upFor": "Coffee and coding"
}
```

### GET /api/users/:userId

Get another user's public profile.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "id": "other-user-uuid",
  "fullName": "Tinashe Moyo",
  "username": "tinashe_m",
  "bio": "Tech enthusiast, startup founder, and coffee lover.",
  "profileImage": "https://example.com/other-profile.jpg",
  "location": "Harare, Zimbabwe",
  "stats": {
    "events": 8,
    "followers": 156,
    "following": 92
  },
  "interests": ["Technology", "Startups", "Cycling"],
  "upFor": "Networking",
  "isFollowedByMe": false
}
```

### PUT /api/users/profile

Update the current user's profile.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "bio": "Updated bio information",
  "interests": ["Technology", "Music", "Photography"],
  "upFor": "Meeting new people"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Profile updated successfully"
}
```

### PUT /api/users/profile-image

Update the user's profile image.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
Content-Type: multipart/form-data
```

**Request Body:**

```
Form data with 'image' field containing the image file
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Profile image updated",
  "imageUrl": "https://example.com/new-profile-image.jpg"
}
```

### GET /api/users/events

Get events related to the current user.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Query Parameters:**

```
type: "created" | "attended" | "saved" (required)
page: number (optional, default: 1)
limit: number (optional, default: 10)
```

**Response (200 OK):**

```json
{
  "events": [
    {
      "id": "event-uuid-1",
      "name": "Tech Conference 2025",
      "date": "2025-05-15",
      "time": "09:00 AM",
      "image": "https://example.com/event-image-1.jpg",
      "location": "Harare Business Hub",
      "attending": 130,
      "created": true
    },
    {
      "id": "event-uuid-2",
      "name": "Art Gallery Opening",
      "date": "2025-04-10",
      "time": "06:00 PM",
      "image": "https://example.com/event-image-2.jpg",
      "location": "National Gallery",
      "attending": 45,
      "created": true
    }
  ],
  "pagination": {
    "total": 15,
    "page": 1,
    "limit": 10,
    "pages": 2
  }
}
```

---

## Events

### GET /api/events

Get a list of events based on filters.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Query Parameters:**

```
category: string (optional)
search: string (optional)
startDate: ISO date string (optional)
endDate: ISO date string (optional)
isPaid: boolean (optional)
location: string (optional)
radius: number in km (optional, default: 10)
lat: number (optional, required if location not provided)
long: number (optional, required if location not provided)
page: number (optional, default: 1)
limit: number (optional, default: 20)
```

**Response (200 OK):**

```json
{
  "events": [
    {
      "id": "event-uuid-1",
      "category": "Business",
      "name": "Startup Pitch Day",
      "date": "2025-04-08",
      "time": "10:30 AM",
      "location": "Harare Business Hub",
      "lat": -17.8127,
      "long": 31.0701,
      "isPaid": false,
      "amount": 0,
      "currency": "USD",
      "owners": ["Harare Startup Community"],
      "attendees": 45,
      "image": "https://example.com/event-image.jpg"
    }
  ],
  "pagination": {
    "total": 42,
    "page": 1,
    "limit": 20,
    "pages": 3
  },
  "categories": ["Business", "Leisure", "Education", "Entertainment"]
}
```

### GET /api/events/categories

Get list of event categories.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "categories": [
    {
      "id": "business",
      "name": "Business",
      "count": 15
    },
    {
      "id": "leisure",
      "name": "Leisure",
      "count": 23
    },
    {
      "id": "education",
      "name": "Education",
      "count": 18
    },
    {
      "id": "entertainment",
      "name": "Entertainment",
      "count": 31
    }
  ]
}
```

### GET /api/events/:eventId

Get detailed information about a specific event.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "id": "event-uuid-1",
  "title": "Startup Pitch Day",
  "description": "Join us for an exciting day of startup pitches from Zimbabwe's most innovative entrepreneurs. Network with investors and fellow founders.",
  "category": "Business",
  "date": "2025-04-08",
  "startTime": "10:30 AM",
  "endTime": "04:00 PM",
  "location": "Harare Business Hub",
  "address": "123 Business Avenue, Harare",
  "lat": -17.8127,
  "long": 31.0701,
  "isPaid": false,
  "amount": 0,
  "currency": "USD",
  "coverImage": "https://example.com/event-cover.jpg",
  "gallery": ["https://example.com/event-image-1.jpg", "https://example.com/event-image-2.jpg"],
  "organizers": [
    {
      "id": "org-uuid-1",
      "name": "Harare Startup Community",
      "logo": "https://example.com/org-logo.jpg"
    }
  ],
  "attendees": {
    "count": 45,
    "friends": [
      {
        "id": "user-uuid-1",
        "name": "Tinashe Moyo",
        "profileImage": "https://example.com/profile-1.jpg"
      }
    ]
  },
  "ticketInfo": {
    "available": true,
    "ticketTypes": []
  },
  "isAttending": false,
  "isSaved": true
}
```

### POST /api/events

Create a new event.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "title": "Tech Meetup 2025",
  "description": "Monthly tech meetup for developers and tech enthusiasts",
  "location": "Innovation Hub, Harare",
  "locationData": {
    "coordinates": [31.0701, -17.8127],
    "name": "Innovation Hub",
    "address": "45 Tech Street, Harare"
  },
  "coverImage": "base64-encoded-image-or-url",
  "startDateTime": "2025-05-20T18:00:00Z",
  "endDateTime": "2025-05-20T21:00:00Z",
  "eventType": "Technology",
  "visibility": "Public",
  "isPaid": true,
  "ticketSetup": {
    "hasLevels": true,
    "levels": [
      {
        "type": "Basic",
        "quantity": 50,
        "price": 5
      },
      {
        "type": "VIP",
        "quantity": 10,
        "price": 15
      }
    ]
  }
}
```

**Response (201 Created):**

```json
{
  "success": true,
  "message": "Event created successfully",
  "eventId": "event-uuid-123"
}
```

### PUT /api/events/:eventId

Update an existing event.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "title": "Updated Tech Meetup 2025",
  "description": "Updated description",
  "startDateTime": "2025-05-21T18:00:00Z"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Event updated successfully"
}
```

### DELETE /api/events/:eventId

Delete an event.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Event deleted successfully"
}
```

### POST /api/events/:eventId/attend

Register to attend an event.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "ticketType": "Basic" // Optional, only for paid events with ticket levels
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Successfully registered for event",
  "ticketId": "ticket-uuid-123" // For paid events
}
```

### DELETE /api/events/:eventId/attend

Cancel attendance to an event.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Event attendance cancelled"
}
```

### POST /api/events/:eventId/save

Save an event to user's saved list.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Event saved successfully"
}
```

### DELETE /api/events/:eventId/save

Remove an event from user's saved list.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Event removed from saved list"
}
```

---

## Maps & Location

### GET /api/maps/events

Get events for map display.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Query Parameters:**

```
lat: number (required)
long: number (required)
radius: number in km (optional, default: 10)
category: string (optional)
```

**Response (200 OK):**

```json
{
  "events": [
    {
      "id": "event-uuid-1",
      "name": "Startup Pitch Day",
      "category": "Business",
      "lat": -17.8127,
      "long": 31.0701,
      "date": "2025-04-08",
      "time": "10:30 AM",
      "isPaid": false
    },
    {
      "id": "event-uuid-2",
      "name": "Street Food Festival",
      "category": "Leisure",
      "lat": -17.8443,
      "long": 31.0495,
      "date": "2025-04-09",
      "time": "11:00 AM",
      "isPaid": false
    }
  ]
}
```

### GET /api/maps/people

Get nearby people for map display.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Query Parameters:**

```
lat: number (required)
long: number (required)
radius: number in km (optional, default: 5)
```

**Response (200 OK):**

```json
{
  "people": [
    {
      "id": "user-uuid-1",
      "name": "Tinashe Moyo",
      "lat": -17.8127,
      "long": 31.0701,
      "profilePhoto": "https://example.com/profile-1.jpg",
      "isOnline": true,
      "upFor": "Coffee and networking"
    }
  ]
}
```

### POST /api/maps/location

Update user's current location.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "lat": -17.8127,
  "long": 31.0701,
  "shareLocation": true
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Location updated successfully"
}
```

---

## Social Features

### GET /api/social/friends

Get user's friends list.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Query Parameters:**

```
page: number (optional, default: 1)
limit: number (optional, default: 20)
```

**Response (200 OK):**

```json
{
  "friends": [
    {
      "id": "user-uuid-1",
      "name": "Tinashe Moyo",
      "username": "tinashe_m",
      "profileImage": "https://example.com/profile-1.jpg",
      "isOnline": true,
      "lastActive": "2025-04-01T10:30:00Z"
    }
  ],
  "pagination": {
    "total": 243,
    "page": 1,
    "limit": 20,
    "pages": 13
  }
}
```

### GET /api/social/friend-requests

Get pending friend requests.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "incoming": [
    {
      "id": "request-uuid-1",
      "user": {
        "id": "user-uuid-2",
        "name": "Chido Nyakudya",
        "username": "chido_n",
        "profileImage": "https://example.com/profile-2.jpg"
      },
      "sentAt": "2025-04-01T08:15:00Z"
    }
  ],
  "outgoing": [
    {
      "id": "request-uuid-2",
      "user": {
        "id": "user-uuid-3",
        "name": "Tatenda Mhaka",
        "username": "tatenda_m",
        "profileImage": "https://example.com/profile-3.jpg"
      },
      "sentAt": "2025-04-02T14:30:00Z"
    }
  ]
}
```

### POST /api/social/friend-requests

Send a friend request.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "userId": "user-uuid-4"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Friend request sent"
}
```

### PUT /api/social/friend-requests/:requestId

Accept or reject a friend request.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "action": "accept" // or "reject"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Friend request accepted"
}
```

### DELETE /api/social/friends/:friendId

Remove a friend.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Friend removed successfully"
}
```

### POST /api/social/status

Update user's "Up For" status.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "status": "Coffee and coding"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Status updated successfully"
}
```

---

## Notifications

### GET /api/notifications

Get user notifications.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Query Parameters:**

```
page: number (optional, default: 1)
limit: number (optional, default: 20)
```

**Response (200 OK):**

```json
{
  "notifications": [
    {
      "id": "notif-uuid-1",
      "type": "friend_request",
      "title": "New Friend Request",
      "message": "Tinashe Moyo sent you a friend request",
      "data": {
        "userId": "user-uuid-1",
        "requestId": "request-uuid-1"
      },
      "isRead": false,
      "createdAt": "2025-04-01T10:30:00Z"
    },
    {
      "id": "notif-uuid-2",
      "type": "event_reminder",
      "title": "Event Tomorrow",
      "message": "Reminder: Startup Pitch Day is tomorrow at 10:30 AM",
      "data": {
        "eventId": "event-uuid-1"
      },
      "isRead": true,
      "createdAt": "2025-04-07T09:00:00Z"
    }
  ],
  "unreadCount": 1,
  "pagination": {
    "total": 42,
    "page": 1,
    "limit": 20,
    "pages": 3
  }
}
```

### PUT /api/notifications/:notificationId/read

Mark a notification as read.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Notification marked as read"
}
```

### PUT /api/notifications/read-all

Mark all notifications as read.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "All notifications marked as read"
}
```

### POST /api/notifications/settings

Update notification settings.

**Request Headers:**

```
Authorization: Bearer jwt-token-here
```

**Request Body:**

```json
{
  "pushEnabled": true,
  "emailEnabled": true,
  "eventReminders": true,
  "friendRequests": true,
  "eventInvitations": true,
  "nearbyEvents": true
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Notification settings updated"
}
```

---

## User Story

### New User Journey

1. **Registration & Onboarding**

   - User downloads the Social Events app
   - User creates an account with email and password (`POST /api/auth/register`)
   - User verifies email with code (`POST /api/auth/verify-email`)
   - User completes profile setup (`POST /api/users/profile-setup`)
     - Sets username
     - Selects interests
     - Chooses event preferences
   - User grants location permissions

2. **Exploring Events**

   - User lands on the home screen with map view
   - App fetches nearby events based on location (`GET /api/maps/events`)
   - User browses events by category
   - User searches for specific events (`GET /api/events`)
   - User views event details (`GET /api/events/:eventId`)

3. **Social Interaction**

   - User discovers people nearby (`GET /api/maps/people`)
   - User sends friend requests (`POST /api/social/friend-requests`)
   - User accepts incoming friend requests (`PUT /api/social/friend-requests/:requestId`)
   - User updates "Up For" status (`POST /api/social/status`)

4. **Event Participation**

   - User registers to attend an event (`POST /api/events/:eventId/attend`)
   - User saves events for later (`POST /api/events/:eventId/save`)
   - User receives event reminders via notifications

5. **Creating Events**

   - User creates their own event (`POST /api/events`)
   - User invites friends to the event
   - User manages event details and attendees

6. **Ongoing Engagement**
   - User receives notifications about friend activities and nearby events
   - User checks their profile to see attended and created events
   - User discovers new events based on interests and past attendance

### Returning User Journey

1. **Authentication**

   - User opens the app and logs in (`POST /api/auth/login`)
   - App retrieves user profile and preferences (`GET /api/users/profile`)

2. **Home Screen**

   - User sees map with nearby events and people
   - User checks notifications for updates (`GET /api/notifications`)
   - User views upcoming events they're attending

3. **Social Updates**

   - User checks friend requests and updates
   - User sees what events friends are attending
   - User updates their "Up For" status to connect with friends

4. **Event Discovery**
   - User browses new events in their area
   - User receives personalized event recommendations
   - User saves or registers for interesting events

This API structure supports a seamless social events platform where users can discover events, connect with friends, and organize their own gatherings, all with location-based features for enhanced real-world social interactions.
