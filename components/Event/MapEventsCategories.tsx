import React from 'react';
import { Pressable, ScrollView, Text, View } from 'react-native';
import { verticalScale } from 'react-native-size-matters';

import { useColorScheme } from '~/lib/useColorScheme';
import { useEvent } from '~/providers/MapProvider';

const MapEventCategories: React.FC = () => {
  const { colors } = useColorScheme();
  const { categories, searchQuery, setSearchQuery, isLoadingCategories, categoriesError } =
    useEvent();

  // Show loading state
  if (isLoadingCategories) {
    return (
      <View
        style={{
          position: 'absolute',
          top: verticalScale(90),
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: 1,
          backgroundColor: 'transparent',
          height: verticalScale(40),
        }}>
        <View className="px-[20px] py-2">
          <View
            style={{
              backgroundColor: colors.background,
              paddingVertical: 8,
              paddingHorizontal: 16,
              borderRadius: 20,
              marginRight: 10,
            }}>
            <Text style={{ color: colors.foreground }} className="font-medium text-body">
              Loading...
            </Text>
          </View>
        </View>
      </View>
    );
  }

  // Show error state
  if (categoriesError) {
    return (
      <View
        style={{
          position: 'absolute',
          top: verticalScale(90),
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: 1,
          backgroundColor: 'transparent',
          height: verticalScale(40),
        }}>
        <View className="px-[20px] py-2">
          <View
            style={{
              backgroundColor: colors.destructive,
              paddingVertical: 8,
              paddingHorizontal: 16,
              borderRadius: 20,
              marginRight: 10,
            }}>
            <Text style={{ color: 'white' }} className="font-medium text-body">
              Error loading categories
            </Text>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View
      style={{
        position: 'absolute',
        top: verticalScale(90),
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 1,
        backgroundColor: 'transparent',
        height: verticalScale(40),
      }}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} className="px-[20px]">
        {categories.map((item, index) => (
          <Pressable
            key={index}
            onPress={() => {
              setSearchQuery(item);
            }}>
            <View
              style={{
                backgroundColor: searchQuery === item ? colors.primary : colors.background,
                paddingVertical: 8,
                paddingHorizontal: 16,
                borderRadius: 20,
                marginRight: 10,
              }}>
              <Text
                style={{ color: searchQuery === item ? 'white' : colors.foreground }}
                className="font-medium text-body">
                {item.charAt(0).toUpperCase() + item.slice(1).toLowerCase()}
              </Text>
            </View>
          </Pressable>
        ))}
      </ScrollView>
    </View>
  );
};

export default MapEventCategories;
