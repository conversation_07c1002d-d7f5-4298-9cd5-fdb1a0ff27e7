import { MarkerView } from '@rnmapbox/maps';
import { useCallback } from 'react';
import { View, Image, TouchableOpacity, Text } from 'react-native';

import { foodPin, businessPin, defaultPin } from '~/assets/Pins';
import { useEvent } from '~/providers/MapProvider';
import { EventType } from '~/types';

function EventMarkersPage() {
  const { setSelectedEvent, filteredEvents, isLoadingEvents } = useEvent();

  // Memoize the marker image function to prevent recreation on every render
  const getMarkerImage = useCallback((category: string) => {
    switch (category) {
      case 'Food':
        return foodPin;
      case 'Business':
        return businessPin;
      default:
        return defaultPin;
    }
  }, []);

  // Memoize the marker emoji function to prevent recreation on every render
  const getMarkerEmoji = useCallback((category: string) => {
    switch (category) {
      case 'EDUCATION':
        return '📚';
      case 'BUSINESS':
        return '💼';
      case 'LEISURE':
        return '🎉';
      case 'ENTERTAINMENT':
        return '🍿';
      default:
        return '📍';
    }
  }, []);

  // Memoize the marker press handler to prevent recreation on every render
  const onMarkerPress = useCallback(
    (event: EventType) => {
      setSelectedEvent(event);
    },
    [setSelectedEvent]
  );

  // Don't render markers while events are loading
  if (isLoadingEvents || filteredEvents.length === 0) {
    return null;
  }

  return (
    <>
      {filteredEvents.map((event) => (
        <MarkerView
          key={event.id}
          coordinate={[event.locationData.coordinates[0], event.locationData.coordinates[1]]}
          anchor={{ x: 0.5, y: 1 }}
          allowOverlap
          allowOverlapWithPuck>
          <View pointerEvents="auto">
            <TouchableOpacity onPressIn={() => onMarkerPress(event)}>
              <View style={{ position: 'relative', width: 50, height: 50 }}>
                <Image
                  source={getMarkerImage(event.eventType)}
                  style={{ width: 50, height: 50 }}
                  resizeMode="contain"
                />
                <Text
                  style={{
                    position: 'absolute',
                    top: 9,
                    left: 0,
                    right: 0,
                    textAlign: 'center',
                    fontSize: 18,
                  }}>
                  {getMarkerEmoji(event.eventType)}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </MarkerView>
      ))}
    </>
  );
}

export default function EventMarkers() {
  return <EventMarkersPage />;
}
