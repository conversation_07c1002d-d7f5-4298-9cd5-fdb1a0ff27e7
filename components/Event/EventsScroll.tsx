import React, { useMemo, useCallback, memo } from 'react';
import { Dimensions, Pressable, Text, View, Image, Platform } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { verticalScale, moderateScale } from 'react-native-size-matters';
import Carousel from 'react-native-reanimated-carousel';

import { useColorScheme } from '~/lib/useColorScheme';
import { getEventStatus } from '~/lib/eventStatusUtils';
import { useEvent, useEventValue } from '~/providers/MapProvider';
import { EventType } from '~/types';

// Helper function to format price - memoized outside component
const formatPrice = (price: number | null, currency: string, isPaid: boolean) => {
  if (!isPaid) return 'Free';
  if (price === null) return '';
  return `${currency}${price.toFixed(2)}`;
};

// Helper function to get a placeholder rating - memoized outside component
const getPlaceholderRating = (id: number) => {
  // Generate a consistent rating between 3.5 and 5.0 based on the event ID
  return ((id % 15) / 10 + 3.5).toFixed(1);
};

// Memoized placeholders object to prevent recreation on each render
const placeholders: Record<string, string> = {
  Business: 'https://images.unsplash.com/photo-1565398305935-49e5dcc5c9f6?q=80&w=240&auto=format',
  Leisure: 'https://images.unsplash.com/photo-1517457373958-b7bdd4587205?q=80&w=240&auto=format',
  Entertainment:
    'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?q=80&w=240&auto=format',
  Educational:
    'https://images.unsplash.com/photo-1524178232363-1fb2b075b655?q=80&w=240&auto=format',
};

// Helper function to get event image or placeholder - memoized outside component
const getEventImage = (event: EventType) => {
  if (event.coverImage) return event.coverImage;
  return (
    placeholders[event.eventType] || 'https://images.unsplash.com/photo-1540575467063-178a50c2df87'
  );
};

// Memoized event card component to prevent unnecessary rerenders
const EventCard = memo(
  ({
    item,
    onPress,
    colors,
  }: {
    item: EventType;
    onPress: (item: EventType) => void;
    colors: any;
  }) => {
    // Memoize computed values
    const rating = useMemo(() => getPlaceholderRating(item.id), [item.id]);
    const price = useMemo(
      () => formatPrice(item.ticketSetup.price, item.currency, item.isPaid),
      [item.ticketSetup.price, item.currency, item.isPaid]
    );
    const imageUri = useMemo(() => getEventImage(item), [item]);

    return (
      <Pressable onPress={() => onPress(item)}>
        <View
          style={{
            backgroundColor: colors.background,
            shadowColor: colors.background,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          }}
          className="ml-[16px] h-28 w-[80vw] flex-row items-center rounded-lg p-4">
          {/* Event Logo/Image */}
          <View className="relative h-full w-[80px] items-center justify-center">
            <Image
              source={{ uri: imageUri }}
              className="h-[60px] w-[60px] rounded-md"
              resizeMode="cover"
              style={{
                aspectRatio: 1,
                backgroundColor: colors.muted,
              }}
            />

            {/* Event Status Chip */}
            {(() => {
              const eventStatus = getEventStatus(item?.startDateTime, item?.endDateTime);

              if (!eventStatus.status) return null;

              return (
                <View
                  className="absolute -right-1 -top-1 rounded-full px-1.5 py-0.5"
                  style={{ backgroundColor: eventStatus.backgroundColor }}>
                  <Text className="font-bold text-xs text-white">{eventStatus.status}</Text>
                </View>
              );
            })()}
          </View>

          {/* Event Details */}
          <View className="flex-1 justify-center py-3 pl-3 pr-3">
            <Text
              style={{ color: colors.foreground }}
              className="mb-1 font-bold text-lg"
              numberOfLines={1}>
              {item.title}
            </Text>

            <Text style={{ color: colors.grey }} className="mb-1 text-sm" numberOfLines={1}>
              {item.location}
            </Text>

            <View className="mt-1 flex-row items-center">
              <View className="mr-3 flex-row items-center">
                <MaterialIcons name="star" size={16} color="#FFC107" />
                <Text style={{ color: colors.foreground }} className="ml-1">
                  {rating}
                </Text>
              </View>

              <View className="flex-row items-center">
                <Text
                  style={{ color: item.isPaid ? colors.primary : '#4CAF50' }}
                  className="font-medium">
                  {item.isPaid ? 'Paid' : 'Free'}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </Pressable>
    );
  }
);

const EventsScroll: React.FC = () => {
  const { colors } = useColorScheme();
  // Use selective context values to prevent unnecessary rerenders
  const filteredEvents = useEventValue('filteredEvents');
  const zoomToLocation = useEventValue('zoomToLocation');
  const setSelectedEvent = useEventValue('setSelectedEvent');

  // Memoize window width to prevent recalculation on every render
  const { width } = useMemo(() => Dimensions.get('window'), []);

  // Memoize carousel config to prevent recreation on every render
  const carouselConfig = useMemo(
    () => ({
      height: verticalScale(Platform.OS === 'ios' ? 100 : 110),
      width,
      mode: 'parallax' as const,
      modeConfig: {
        parallaxScrollingScale: 1,
        parallaxScrollingOffset: moderateScale(60),
      },
    }),
    [width]
  );

  // Memoize event handlers to prevent recreation on every render
  const handleSnapToItem = useCallback(
    (item: number) => {
      const coordinates = {
        latitude: filteredEvents[item].locationData.coordinates[1],
        longitude: filteredEvents[item].locationData.coordinates[0],
      };
      zoomToLocation(coordinates);
    },
    [filteredEvents, zoomToLocation]
  );

  const handleSelectEvent = useCallback(
    (item: EventType) => {
      setSelectedEvent(item);
    },
    [setSelectedEvent]
  );

  // Memoize render item function to prevent recreation on every render
  const renderItem = useCallback(
    ({ item }: { item: EventType }) => {
      return <EventCard item={item} onPress={handleSelectEvent} colors={colors} />;
    },
    [colors, handleSelectEvent]
  );

  return (
    <View
      style={{
        zIndex: 0,
        bottom: verticalScale(Platform.OS === 'ios' ? -4 : 4),
        width: '100%',
        position: 'absolute',
        backgroundColor: 'transparent',
      }}>
      <Carousel
        data={filteredEvents}
        height={carouselConfig.height}
        loop
        snapEnabled
        width={carouselConfig.width}
        style={{ width }}
        mode={carouselConfig.mode}
        modeConfig={carouselConfig.modeConfig}
        onSnapToItem={handleSnapToItem}
        renderItem={renderItem}
      />
    </View>
  );
};

// Wrap the component with memo to prevent unnecessary rerenders
export default memo(EventsScroll);
