import { Enty<PERSON>, FontAwesome5, FontAwesome6, MaterialCommunityIcons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Text, View, TouchableOpacity, Animated } from 'react-native';

import { useColorScheme } from '~/lib/useColorScheme';

import { useEvent } from '~/providers/MapProvider';
import { Button, ButtonText } from '../ui/button';

const EventsSheetContent: React.FC = () => {
  const { filteredEvents } = useEvent();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const { colors } = useColorScheme();
  const [currentIndex, setCurrentIndex] = useState(0);

  // Create Animated Value for rotation
  const rotateAnim = useRef(new Animated.Value(0)).current;

  // Interpolate rotation for chevron
  const chevronRotation = rotateAnim.interpolate({
    inputRange: [0, 2],
    outputRange: ['0deg', '180deg'],
  });

  // Custom Handle Component with Animated Rotation
  const CustomHandle = useCallback(() => {
    return (
      <View>
        <Animated.View
          style={{
            transform: [{ rotate: chevronRotation }],
            alignItems: 'center',
            padding: 0,
          }}>
          <FontAwesome5 name="chevron-up" size={20} color={colors.grey} />
        </Animated.View>
      </View>
    );
  }, [chevronRotation, colors.foreground]);

  // Handle Sheet Changes with Animation
  const handleSheetChanges = useCallback(
    (index: number) => {
      setCurrentIndex(index);

      // Animate chevron rotation
      Animated.timing(rotateAnim, {
        toValue: index > 0 ? 2 : 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    },
    [rotateAnim]
  );

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={0}
      snapPoints={['6.5%', '80%']}
      backgroundStyle={{ backgroundColor: colors.card }}
      enableOverDrag={false}
      onChange={handleSheetChanges}
      handleComponent={CustomHandle}
      onAnimate={(fromIndex, toIndex) => {
        // Animate chevron based on sheet position
        Animated.timing(rotateAnim, {
          toValue: toIndex > 0 ? 2 : 0, // Rotate based on sheet height
          duration: 300,
          useNativeDriver: true,
        }).start();
      }}
      enablePanDownToClose={false}
      containerStyle={{
        zIndex: 90, // Very high z-index
        elevation: 90, // For Android
        position: 'absolute', // Ensure it's positioned above other elements
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
      style={{
        elevation: 10, // Stronger elevation for Android
        shadowColor: colors.foreground,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 6,
      }}
      bottomInset={0}
      handleIndicatorStyle={{
        backgroundColor: colors.foreground,
      }}>
      <BottomSheetView className="flex h-full justify-between">
        <View className="items-center px-4">
          <View className="flex flex-row items-center gap-1">
            <Text className="light:text-light-text font-medium text-body dark:text-dark-text">
              {filteredEvents.length} events nearby
            </Text>
          </View>
        </View>
      </BottomSheetView>
    </BottomSheet>
  );
};

export default function EventsSheet() {
  return <EventsSheetContent />;
}
