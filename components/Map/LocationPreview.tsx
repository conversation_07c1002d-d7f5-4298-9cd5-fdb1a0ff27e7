import React, { useRef, useState, useEffect } from 'react';
import { View, TouchableOpacity } from 'react-native';
import Mapbox, { Camera, MapView, MarkerView, PointAnnotation } from '@rnmapbox/maps';
import { MaterialIcons } from '@expo/vector-icons';
import { Text } from '~/components/nativewindui/Text';
import { useColorScheme } from '~/lib/useColorScheme';
import type { LocationData } from './LocationPicker';

type LocationPreviewProps = {
  location: LocationData;
  onLocationChange?: (location: LocationData) => void;
  editable?: boolean;
};

const LocationPreview: React.FC<LocationPreviewProps> = ({
  location,
  onLocationChange,
  editable = false,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const mapRef = useRef<MapView>(null);
  const [currentLocation, setCurrentLocation] = useState<LocationData>(location);

  const mapStyle = isDark
    ? 'mapbox://styles/mapbox/dark-v11'
    : 'mapbox://styles/mapbox/outdoors-v12';

  useEffect(() => {
    setCurrentLocation(location);
  }, [location]);

  const handleMapPress = async (event: any) => {
    if (!editable) return;

    const coordinates = event.geometry.coordinates;

    // Update the location with the new coordinates
    const updatedLocation = {
      ...currentLocation,
      coordinates,
      address: 'Selected location', // You might want to reverse geocode this in a real app
    };

    setCurrentLocation(updatedLocation);

    if (onLocationChange) {
      onLocationChange(updatedLocation);
    }
  };

  return (
    <View
      className={`overflow-hidden rounded-xl ${isDark ? 'bg-gray-800' : 'bg-white'} border shadow-sm ${isDark ? 'border-gray-700' : 'border-gray-200'}`}
      style={{ height: 150 }}>
      <MapView
        ref={mapRef}
        style={{ flex: 1 }}
        styleURL={mapStyle}
        scaleBarEnabled={false}
        logoEnabled={false}
        attributionEnabled={false}
        scrollEnabled
        rotateEnabled
        pitchEnabled
        zoomEnabled
        onPress={editable ? handleMapPress : undefined}>
        <Camera
          centerCoordinate={currentLocation.coordinates}
          zoomLevel={15}
          animationMode="none"
          animationDuration={0}
        />

        <MarkerView coordinate={currentLocation.coordinates}>
          <View className="items-center">
            <MaterialIcons name="location-pin" size={50} color="#8b5cf6" />
          </View>
        </MarkerView>
      </MapView>

      <View
        className={`absolute bottom-0 left-0 right-0 p-2 ${isDark ? 'bg-gray-800/80' : 'bg-white/80'}`}>
        {currentLocation.manualAddress && (
          <Text
            className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'} mt-0.5 font-medium`}
            numberOfLines={1}>
            {currentLocation.manualAddress}
          </Text>
        )}
      </View>

      {editable && (
        <View className="absolute right-2 top-2">
          <TouchableOpacity
            className={`rounded-full p-2 ${isDark ? 'bg-gray-700' : 'bg-white'}`}
            onPress={() =>
              mapRef.current?.getCenter().then((center) => {
                const updatedLocation = {
                  ...currentLocation,
                  coordinates: center,
                };
                setCurrentLocation(updatedLocation);
                if (onLocationChange) onLocationChange(updatedLocation);
              })
            }>
            <MaterialIcons name="my-location" size={22} color={isDark ? '#fff' : '#333'} />
          </TouchableOpacity>
        </View>
      )}

      {editable && (
        <Text
          className={`absolute left-2 top-2 rounded px-2 py-1 ${isDark ? 'bg-gray-700 text-white' : 'bg-white text-gray-800'} text-xs`}>
          Tap on map to select location
        </Text>
      )}
    </View>
  );
};

export default LocationPreview;
