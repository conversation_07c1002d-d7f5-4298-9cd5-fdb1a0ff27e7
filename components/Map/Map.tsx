import Mapbox, { Camera, LocationPuck, MapView } from '@rnmapbox/maps';
import * as Location from 'expo-location';
import React, { useCallback, useMemo, memo, useRef, useEffect, useState } from 'react';

import EventMarkers from '../Event/EventMarkers';
import MapLoader from './MapLoader';
import PeopleMarker from '../People/PeopleMarkers';

import { useColorScheme } from '~/lib/useColorScheme';
import { useEventValue } from '~/providers/MapProvider';
import { MapTsTypeMap } from '~/types';

Mapbox.setAccessToken(
  'pk.eyJ1IjoiZW1hY2xpYW0iLCJhIjoiY2wxNHRpcGNiMGR1dzNla2FndWVpdmJxbyJ9.wnxGUxO6rO3CoGaKyuXbVA'
);

// Memoized marker components to prevent unnecessary rerenders
const MemoizedEventMarkers = memo(EventMarkers);
const MemoizedPeopleMarker = memo(PeopleMarker);

const Map: React.FC = () => {
  // Use selective context values to prevent rerenders when other context values change
  const { isDarkColorScheme } = useColorScheme();
  const cameraRef = useEventValue('cameraRef');
  const followUserLocation = useEventValue('followUserLocation');
  const MapType = useEventValue('MapType');
  const setZoomLevel = useEventValue('setZoomLevel');
  const isLoadingEvents = useEventValue('isLoadingEvents');
  const isLoadingCategories = useEventValue('isLoadingCategories');
  const isLoadingPeople = useEventValue('isLoadingPeople');
  const [mapLoaded, setMapLoaded] = useState(false);
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null);

  // Use ref to store the debounce timeout
  const debounceTimeoutRef = useRef<number | null>(null);

  // Check if any data is loading
  const isLoading = isLoadingEvents || isLoadingCategories || isLoadingPeople || !userLocation;

  useEffect(() => {
    const fetchUserLocation = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          console.log('Location permission denied');
          return;
        }

        const location = await Location.getCurrentPositionAsync({});
        setUserLocation([location.coords.longitude, location.coords.latitude]);
      } catch (error) {
        console.error('Error fetching user location:', error);
      }
    };

    fetchUserLocation();
  }, []);

  useEffect(() => {
    if (cameraRef.current && userLocation && mapLoaded) {
      cameraRef.current.setCamera({
        centerCoordinate: userLocation,
        zoomLevel: followUserLocation ? 12 : 10,
        animationMode: 'none',
        animationDuration: 0,
      });
    }
  }, [userLocation, followUserLocation, mapLoaded]);

  // Memoize map style to prevent recalculation on every render
  const mapStyle = useMemo(
    () =>
      isDarkColorScheme ? 'mapbox://styles/mapbox/dark-v11' : 'mapbox://styles/mapbox/outdoors-v12',
    [isDarkColorScheme]
  );

  // Memoize camera changed handler with debouncing to prevent infinite loops
  const handleCameraChanged = useCallback(
    (e: any) => {
      if (e.properties.zoom) {
        // Clear existing timeout
        if (debounceTimeoutRef.current) {
          clearTimeout(debounceTimeoutRef.current);
        }

        // Set new timeout to debounce the zoom level update
        debounceTimeoutRef.current = setTimeout(() => {
          setZoomLevel(e.properties.zoom);
        }, 1000); // 100ms debounce
      }
    },
    [setZoomLevel]
  );

  // Memoize the markers to prevent unnecessary rerenders
  const mapMarkers = useMemo(() => {
    return MapType === MapTsTypeMap.Events ? <MemoizedEventMarkers /> : <MemoizedPeopleMarker />;
  }, [MapType]);

  return (
    <>
      <MapView
        logoEnabled={false} // Removes Mapbox logo
        attributionEnabled={false} // Removes attribution icon
        scaleBarEnabled={false}
        rotateEnabled
        projection="mercator"
        style={{ flex: 1 }}
        styleURL={mapStyle}
        onCameraChanged={handleCameraChanged}
        onDidFinishLoadingMap={() => {
          setMapLoaded(true);
        }}>
        <Camera ref={cameraRef} />

        <LocationPuck puckBearingEnabled puckBearing="heading" pulsing={{ isEnabled: true }} />
        {mapMarkers}
      </MapView>

      <MapLoader visible={isLoading} />
    </>
  );
};

// Wrap the component with memo to prevent unnecessary rerenders
export default memo(Map);
