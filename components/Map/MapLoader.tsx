import React, { useEffect, useRef } from 'react';
import { View, Image, Animated, Easing } from 'react-native';
import { Text } from '~/components/nativewindui/Text';
import { useColorScheme } from '~/lib/useColorScheme';

interface MapLoaderProps {
  visible: boolean;
}

const MapLoader: React.FC<MapLoaderProps> = ({ visible }) => {
  const { colors, isDark } = useColorScheme();
  const pulseAnim = useRef(new Animated.Value(0.7)).current;
  const fadeAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (visible) {
      // Start pulsing animation
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 0.7,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ]),
        { iterations: -1 }
      );

      pulseAnimation.start();

      return () => {
        pulseAnimation.stop();
      };
    } else {
      // Fade out animation when hiding loader
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, pulseAnim, fadeAnim]);

  if (!visible) {
    return null;
  }

  const logoSource = isDark
    ? require('~/assets/dark_theme_logo.png')
    : require('~/assets/light_theme_logo.png');

  return (
    <Animated.View
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: colors.background,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
        opacity: fadeAnim,
      }}>
      <Animated.View
        style={{
          transform: [{ scale: pulseAnim }],
          marginBottom: 20,
        }}>
        <Image
          source={logoSource}
          style={{
            width: 200,
            height: 200,
            resizeMode: 'contain',
          }}
        />
      </Animated.View>
    </Animated.View>
  );
};

export default MapLoader;
