import React, { useRef } from 'react';
import { Dimensions, Text, View, Image, TouchableOpacity, Platform, Pressable } from 'react-native';
import { useRouter } from 'expo-router';

import { Ionicons } from '@expo/vector-icons';
import Carousel from 'react-native-reanimated-carousel';
import { scale, verticalScale, moderateScale } from 'react-native-size-matters';

import PersonProfileSheet, {
  PersonProfileSheetHandle,
} from '~/components/People/PersonProfileSheet';
import { useEvent } from '~/providers/MapProvider';
import { useColorScheme } from '~/lib/useColorScheme';

const PeopleScroll: React.FC = () => {
  const { colors } = useColorScheme();
  const { People, zoomToLocation, setUserId } = useEvent();
  const { width } = Dimensions.get('window');
  const router = useRouter();
  const personProfileSheetRef = useRef<PersonProfileSheetHandle>(null);

  // Mock data for people with stories
  const peopleWithStories = [2, 3, 7]; // IDs of people who have stories

  const handleViewStory = (userId: number) => {
    router.push({
      pathname: '/story/view',
      params: { userId },
    });
  };

  const handlePersonPress = (person: any) => {
    setUserId(person.id);
  };

  return (
    <View
      style={{
        zIndex: 0,
        bottom: verticalScale(Platform.OS === 'ios' ? -4 : 4),
        width: '100%',
        position: 'absolute',
        backgroundColor: 'transparent',
      }}>
      <PersonProfileSheet
        ref={personProfileSheetRef}
        onViewStory={handleViewStory}
        onChat={(userId) => {
          router.push({
            pathname: '/chat',
            params: { userId, name: People.find((p) => p.id === userId)?.name },
          });
        }}
      />
      <Carousel
        data={People}
        height={verticalScale(Platform.OS === 'ios' ? 100 : 110)}
        loop
        snapEnabled
        width={width}
        style={{
          width,
        }}
        mode="parallax"
        modeConfig={{
          parallaxScrollingScale: 1,
          parallaxScrollingOffset: moderateScale(60),
        }}
        onSnapToItem={(item) => {
          const coordinates = {
            latitude: People[item].lat,
            longitude: People[item].long,
          };
          zoomToLocation(coordinates);
        }}
        onProgressChange={() => {}}
        renderItem={({ item }) => {
          // Format last active time
          const lastActive = item.lastActive ? new Date(item.lastActive) : null;
          const timeAgo = lastActive ? getTimeAgo(lastActive) : '';

          return (
            <Pressable onPress={() => handlePersonPress(item)}>
              <View
                style={{
                  backgroundColor: colors.background,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                  elevation: 3,
                }}
                className="ml-[16px] h-28 w-[80vw] flex-row items-center rounded-lg p-4">
                {/* Avatar */}
                <View
                  style={{
                    height: moderateScale(64),
                    width: moderateScale(64),
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: scale(12),
                  }}>
                  <View
                    style={{
                      backgroundColor: colors.card,
                      height: moderateScale(60),
                      width: moderateScale(60),
                      borderRadius: moderateScale(30),
                      overflow: 'hidden',
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderWidth: peopleWithStories.includes(item.id) ? 2 : 0,
                      borderColor: '#5A4FCF',
                    }}>
                    {item.profilePhoto ? (
                      <Image
                        source={{ uri: item.profilePhoto }}
                        style={{ width: '100%', height: '100%' }}
                        resizeMode="cover"
                      />
                    ) : (
                      <Ionicons name="person" size={moderateScale(30)} color={colors.primary} />
                    )}
                  </View>
                </View>

                {/* Online Indicator */}

                {/* Person Info */}
                <View style={{ flex: 1 }}>
                  <View className="flex-row items-center">
                    <Text
                      style={{ color: colors.foreground }}
                      className="text-lg font-bold"
                      numberOfLines={1}>
                      {item.name || 'Unknown Person'}
                    </Text>
                  </View>

                  {item.bio && (
                    <Text
                      style={{ color: colors.foreground, opacity: 0.8 }}
                      className="text-xs"
                      numberOfLines={1}>
                      {item.bio}
                    </Text>
                  )}

                  {item.interests && item.interests.length > 0 && (
                    <View className="flex-row flex-wrap mt-1">
                      {item.interests.slice(0, 2).map((interest, idx) => (
                        <View
                          key={idx}
                          style={{
                            backgroundColor: colors.card,
                            paddingHorizontal: scale(6),
                            paddingVertical: verticalScale(2),
                            borderRadius: scale(10),
                            marginRight: scale(4),
                          }}>
                          <Text style={{ color: colors.primary }} className="text-xs">
                            {interest}
                          </Text>
                        </View>
                      ))}
                      {item.interests.length > 2 && (
                        <Text
                          style={{ color: colors.foreground, opacity: 0.6 }}
                          className="self-center ml-1 text-xs">
                          +{item.interests.length - 2}
                        </Text>
                      )}
                    </View>
                  )}
                </View>
              </View>
            </Pressable>
          );
        }}
      />
    </View>
  );
};

// Helper function to format time ago
const getTimeAgo = (date: Date): string => {
  const now = new Date();
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (seconds < 60) return 'just now';

  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) return `${minutes}m ago`;

  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}h ago`;

  const days = Math.floor(hours / 24);
  if (days < 7) return `${days}d ago`;

  return date.toLocaleDateString();
};

export default PeopleScroll;
