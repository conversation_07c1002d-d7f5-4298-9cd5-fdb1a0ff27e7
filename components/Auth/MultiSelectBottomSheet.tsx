import React, { useRef, forwardRef, useImperativeHandle } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import BottomSheet, {
  BottomSheetView,
  BottomSheetBackdrop,
  BottomSheetModal,
} from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '../RenderBackdrop';
import { Button } from '../ui/button';

export interface MultiSelectBottomSheetProps {
  title: string;
  options: string[];
  selectedItems: string[];
  onSave: (selectedItems: string[]) => void;
}

export interface MultiSelectBottomSheetHandle {
  present: () => void;
  dismiss: () => void;
}

const MultiSelectBottomSheet = forwardRef<
  MultiSelectBottomSheetHandle,
  MultiSelectBottomSheetProps
>(({ title, options, selectedItems, onSave }, ref) => {
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const [selected, setSelected] = React.useState<string[]>([]);
  const { colorScheme, colors } = useColorScheme();
  const isDark = colorScheme === 'dark';

  useImperativeHandle(ref, () => ({
    present: () => {
      setSelected([...selectedItems]);
      bottomSheetModalRef.current?.expand();
    },
    dismiss: () => bottomSheetModalRef.current?.close(),
  }));

  const toggleOption = (option: string) => {
    if (selected.includes(option)) {
      setSelected(selected.filter((item) => item !== option));
    } else {
      setSelected([...selected, option]);
    }
  };

  const handleSave = () => {
    onSave(selected);
    bottomSheetModalRef.current?.close();
  };

  function toggleInterest(id: any) {
    throw new Error('Function not implemented.');
  }

  return (
    <BottomSheet
      ref={bottomSheetModalRef}
      index={-1}
      snapPoints={['70%']}
      backdropComponent={RenderBackdrop}
      backgroundStyle={{
        backgroundColor: colors.background,
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
      }}
      handleIndicatorStyle={{
        backgroundColor: isDark ? '#555' : '#999',
        width: 40,
      }}>
      <BottomSheetView className="flex-1 px-5 pt-2">
        <View className="flex-row items-center justify-between mb-5">
          <Text className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
            {title}
          </Text>
          <TouchableOpacity className="p-2" onPress={() => bottomSheetModalRef.current?.close()}>
            <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
          </TouchableOpacity>
        </View>

        <Text className={`mb-4 text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
          Select all that apply
        </Text>

        <ScrollView className="flex-1 mb-4" showsVerticalScrollIndicator={false}>
          <View className="flex-row flex-wrap">
            {options.map((option) => (
              <Button
                key={option}
                className={`m-1 mb-4 rounded-xl border px-3 py-2 ${selected ? 'border-2' : 'border'}`}
                style={{
                  backgroundColor: colors.grey5,
                  borderColor: selected.includes(option) ? colors.primary : colors.grey5,
                }}
                onPress={() => toggleOption(option)}>
                <View className="flex-row items-center justify-center">
                  <Text
                    className={`ml-2 font-medium text-sm `}
                    style={{ color: colors.grey }}
                    numberOfLines={1}>
                    {option}
                  </Text>
                </View>
              </Button>
            ))}
          </View>
        </ScrollView>

        <TouchableOpacity
          onPress={handleSave}
          className="mb-6 h-14 items-center rounded-lg bg-violet-600 py-3.5">
          <Text className="text-base font-semibold text-white">Save</Text>
        </TouchableOpacity>
      </BottomSheetView>
    </BottomSheet>
  );
});

export default MultiSelectBottomSheet;
