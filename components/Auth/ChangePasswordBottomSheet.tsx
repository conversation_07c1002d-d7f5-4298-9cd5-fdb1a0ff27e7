import React, { useRef, forwardRef, useImperative<PERSON>andle, useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  ActivityIndicator,
} from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import BottomSheet, { BottomSheetView, BottomSheetModal } from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '../RenderBackdrop';

// UI Components
import {
  FormControl,
  FormControlError,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
} from '@/components/ui/form-control';
import { Input, InputField, InputSlot } from '~/components/ui/input';
import { But<PERSON>, ButtonText } from '@/components/ui/button';

export interface ChangePasswordBottomSheetProps {
  onSave: (oldPassword: string, newPassword: string) => void;
  isLoading?: boolean;
}

export interface ChangePasswordBottomSheetHandle {
  present: () => void;
  dismiss: () => void;
}

// Validation schema for password change
const passwordChangeSchema = z.object({
  oldPassword: z.string().min(1, 'Current password is required'),
  newPassword: z
    .string()
    .min(8, 'New password must be at least 8 characters long')
    .refine((value) => /\d/.test(value), {
      message: 'New password must include at least one number',
    })
    .refine((value) => /[A-Z]/.test(value), {
      message: 'New password must include at least one uppercase letter',
    }),
});

const ChangePasswordBottomSheet = forwardRef<
  ChangePasswordBottomSheetHandle,
  ChangePasswordBottomSheetProps
>(({ onSave, isLoading }, ref) => {
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);

  // Create form with validation
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(passwordChangeSchema),
    defaultValues: {
      oldPassword: '',
      newPassword: '',
    },
  });

  useImperativeHandle(ref, () => ({
    present: () => {
      console.log('Attempting to present change password bottom sheet');
      reset({ oldPassword: '', newPassword: '' });
      if (bottomSheetModalRef.current) {
        bottomSheetModalRef.current.expand();
        console.log('Change password bottom sheet present method called');
      } else {
        console.log('Change password bottom sheet ref is null');
      }
    },
    dismiss: () => {
      if (bottomSheetModalRef.current) {
        bottomSheetModalRef.current.close();
        console.log('Change password bottom sheet dismiss method called');
      } else {
        console.log('Change password bottom sheet ref is null when dismissing');
      }
    },
  }));

  const handleSave = (data: { oldPassword: string; newPassword: string }) => {
    console.log('Saving password change');
    Keyboard.dismiss(); // Dismiss keyboard immediately when form is submitted
    onSave(data.oldPassword, data.newPassword);
    bottomSheetModalRef.current?.close();
  };

  const [snapPoints, setSnapPoints] = React.useState([500]); // Default snap point for two fields

  useEffect(() => {
    const onKeyboardShow = (event: any) => {
      const keyboardHeight = event.endCoordinates.height;
      setSnapPoints([350 + keyboardHeight]); // Adjust snap point based on keyboard height
    };

    const onKeyboardHide = () => {
      setSnapPoints([450]); // Reset to default snap point
    };

    const keyboardShowListener = Keyboard.addListener('keyboardDidShow', onKeyboardShow);
    const keyboardHideListener = Keyboard.addListener('keyboardDidHide', onKeyboardHide);

    return () => {
      keyboardShowListener.remove();
      keyboardHideListener.remove();
    };
  }, []);

  return (
    <BottomSheet
      ref={bottomSheetModalRef}
      index={-1}
      snapPoints={snapPoints}
      backdropComponent={RenderBackdrop}
      enablePanDownToClose={true}
      enableDynamicSizing={true}
      backgroundStyle={{
        backgroundColor: colors.background,
      }}
      handleIndicatorStyle={{
        backgroundColor: isDark ? '#555' : '#999',
        width: 40,
      }}
      onChange={(index) => {
        console.log(`Change password bottom sheet changed to index: ${index}`);
      }}>
      <BottomSheetView className="flex-1 px-5 pt-2">
        <View className="mb-5 flex-row items-center justify-between">
          <Text className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
            Change Password
          </Text>
          <TouchableOpacity
            className="p-2"
            onPress={() => {
              bottomSheetModalRef.current?.close();
              Keyboard.dismiss();
            }}>
            <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
          </TouchableOpacity>
        </View>

        {/* Current Password Field */}
        <FormControl isInvalid={!!errors.oldPassword} className="mb-4">
          <FormControlLabel>
            <FormControlLabelText
              className={`mb-1.5 text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
              Current Password
            </FormControlLabelText>
          </FormControlLabel>
          <Controller
            name="oldPassword"
            control={control}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input
                variant="outline"
                size="md"
                className="h-14 rounded-lg border-0"
                style={{ backgroundColor: colors.grey5 }}>
                <InputField
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  secureTextEntry={!showOldPassword}
                  className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                  placeholder="Enter your current password"
                  placeholderTextColor={isDark ? colors.grey : colors.grey}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <InputSlot className="pr-3">
                  <TouchableOpacity onPress={() => setShowOldPassword(!showOldPassword)}>
                    <Ionicons
                      name={showOldPassword ? 'eye-off' : 'eye'}
                      size={18}
                      color={isDark ? '#9ca3af' : '#6b7280'}
                    />
                  </TouchableOpacity>
                </InputSlot>
              </Input>
            )}
          />
          <FormControlError>
            <FormControlErrorText className="mt-1 text-sm text-red-500">
              {errors.oldPassword?.message}
            </FormControlErrorText>
          </FormControlError>
        </FormControl>

        {/* New Password Field */}
        <FormControl isInvalid={!!errors.newPassword} className="mb-4">
          <FormControlLabel>
            <FormControlLabelText
              className={`mb-1.5 text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
              New Password
            </FormControlLabelText>
          </FormControlLabel>
          <Controller
            name="newPassword"
            control={control}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input
                variant="outline"
                size="md"
                className="h-14 rounded-lg border-0"
                style={{ backgroundColor: colors.grey5 }}>
                <InputField
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  secureTextEntry={!showNewPassword}
                  className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                  placeholder="Enter your new password"
                  placeholderTextColor={isDark ? colors.grey : colors.grey}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <InputSlot className="pr-3">
                  <TouchableOpacity onPress={() => setShowNewPassword(!showNewPassword)}>
                    <Ionicons
                      name={showNewPassword ? 'eye-off' : 'eye'}
                      size={18}
                      color={isDark ? '#9ca3af' : '#6b7280'}
                    />
                  </TouchableOpacity>
                </InputSlot>
              </Input>
            )}
          />
          <FormControlError>
            <FormControlErrorText className="mt-1 text-sm text-red-500">
              {errors.newPassword?.message}
            </FormControlErrorText>
          </FormControlError>
        </FormControl>

        {/* Password Requirements */}
        <View className="mb-4">
          <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            Password requirements:
          </Text>
          <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            • At least 8 characters long
          </Text>
          <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            • Include at least one number
          </Text>
          <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            • Include at least one uppercase letter
          </Text>
        </View>

        <Button
          onPress={handleSubmit(handleSave)}
          className="mt-2 h-14 items-center rounded-lg bg-violet-600 py-3.5">
          <ButtonText className="text-base font-semibold text-white">
            {isLoading ? <ActivityIndicator color="white" /> : 'Change Password'}
          </ButtonText>
        </Button>
      </BottomSheetView>
    </BottomSheet>
  );
});

export default ChangePasswordBottomSheet;
