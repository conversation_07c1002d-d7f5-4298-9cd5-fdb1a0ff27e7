import React, { useRef, forwardRef, useImperativeHandle, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import BottomSheet, {
  BottomSheetView,
  BottomSheetBackdrop,
  BottomSheetModal,
} from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '../RenderBackdrop';
import ImagePicker from 'react-native-image-crop-picker';
import { FileService } from '~/services/FileService';
import { UserStore } from '~/store/store';
import { Toast } from 'toastify-react-native';

export interface PhotoBottomSheetProps {
  currentPhoto: string;
  onPhotoSelected: (uri: string) => void;
  userId: string;
}

export interface PhotoBottomSheetHandle {
  present: () => void;
  dismiss: () => void;
}

const PhotoUploadBottomSheet = forwardRef<PhotoBottomSheetHandle, PhotoBottomSheetProps>(
  ({ currentPhoto, onPhotoSelected, userId }, ref) => {
    const bottomSheetModalRef = useRef<BottomSheetModal>(null);
    const { colorScheme, colors } = useColorScheme();
    const isDark = colorScheme === 'dark';
    const [isUploading, setIsUploading] = useState(false);

    useImperativeHandle(ref, () => ({
      present: () => {
        bottomSheetModalRef.current?.expand();
      },
      dismiss: () => bottomSheetModalRef.current?.close(),
    }));

    const uploadImageFile = async (uri: string) => {
      try {
        setIsUploading(true);

        // Create file object from URI
        const filename = uri.split('/').pop() || 'image.jpg';
        const fileType = filename.split('.').pop() || 'jpg';

        const file = {
          uri,
          type: `image/${fileType}`,
          name: filename,
        } as any;

        const response = await FileService.uploadManyImages([file], userId, 'PROFILE_PICTURE');
        onPhotoSelected(uri);
        (UserStore.getState() as { setUser: (data: any) => void }).setUser(response.body);
        bottomSheetModalRef.current?.close();
        Toast.show({
          type: 'success',
          text1: 'Profile Photo Updated',
          text2: 'Your profile photo has been updated.',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
      } catch (error) {
        console.error('Image upload error:', error);
        Toast.show({
          type: 'error',
          text1: 'Upload Error',
          text2: 'Failed to upload image. Please try again.',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
      } finally {
        setIsUploading(false);
      }
    };

    const takePhoto = async () => {
      try {
        const image = await ImagePicker.openCamera({
          width: 400,
          height: 400,
          cropping: true,
          cropperCircleOverlay: true,
          compressImageQuality: 0.7,
          freeStyleCropEnabled: true,
          includeBase64: false,
        });

        await uploadImageFile(image.path);
      } catch (error: any) {
        if (error.code !== 'E_PICKER_CANCELLED') {
          Alert.alert('Permission required', 'Camera permission is required to take photos');
        }
      }
    };

    const chooseFromLibrary = async () => {
      try {
        const image = await ImagePicker.openPicker({
          width: 400,
          height: 400,
          cropping: true,
          cropperCircleOverlay: true,
          compressImageQuality: 0.7,
          freeStyleCropEnabled: true,
          includeBase64: false,
          mediaType: 'photo',
          cropperStatusBarColor: Platform.OS === 'android' ? colors.background : undefined,
          cropperToolbarColor: Platform.OS === 'android' ? colors.background : undefined,
          cropperToolbarWidgetColor: Platform.OS === 'android' ? colors.foreground : undefined,
          cropperActiveWidgetColor: Platform.OS === 'android' ? colors.primary : undefined,
          showCropGuidelines: Platform.OS === 'android' ? true : undefined,
          showCropFrame: Platform.OS === 'android' ? true : undefined,
          hideBottomControls: Platform.OS === 'android' ? false : undefined,
          enableRotationGesture: Platform.OS === 'android' ? true : undefined,
          disableCropperColorSetters: Platform.OS === 'android' ? false : undefined,
          // iOS-specific configurations
          cropperChooseColor: Platform.OS === 'ios' ? colors.primary : undefined,
          cropperCancelColor: Platform.OS === 'ios' ? colors.foreground : undefined,
          avoidEmptySpaceAroundImage: Platform.OS === 'ios' ? true : undefined,
          cropperToolbarTitle: 'Edit Photo',
        });

        await uploadImageFile(image.path);
      } catch (error: any) {
        if (error.code !== 'E_PICKER_CANCELLED') {
          Alert.alert('Permission required', 'Gallery permission is required to select photos');
        }
      }
    };

    const removePhoto = () => {
      Alert.alert('Remove Profile Photo', 'Are you sure you want to remove your profile photo?', [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            // Use default avatar or placeholder
            onPhotoSelected(
              'https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y'
            );
            bottomSheetModalRef.current?.close();
          },
        },
      ]);
    };

    return (
      <BottomSheet
        ref={bottomSheetModalRef}
        index={-1}
        snapPoints={['60%']}
        backdropComponent={RenderBackdrop}
        backgroundStyle={{
          backgroundColor: colors.background,
          borderTopLeftRadius: 24,
          borderTopRightRadius: 24,
        }}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#555' : '#999',
          width: 40,
        }}>
        <BottomSheetView className="flex-1 px-5 pt-2">
          <View className="flex-row items-center justify-between mb-5">
            <Text className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
              Profile Photo
            </Text>
            <TouchableOpacity className="p-2" onPress={() => bottomSheetModalRef.current?.close()}>
              <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
            </TouchableOpacity>
          </View>

          <View className="flex-row justify-center mb-6">
            <View className="relative">
              <Image source={{ uri: currentPhoto }} className="w-20 h-20 rounded-full" />
              {isUploading && (
                <View className="absolute inset-0 items-center justify-center rounded-full bg-black/50">
                  <ActivityIndicator size="small" color="#fff" />
                </View>
              )}
            </View>
          </View>

          <View className="flex gap-3">
            <TouchableOpacity
              onPress={takePhoto}
              disabled={isUploading}
              className={`h-14 flex-row items-center rounded-lg px-4 py-3 ${isUploading ? 'opacity-40' : ''}`}
              style={{ backgroundColor: isUploading ? colors.grey3 : colors.grey5 }}>
              <Ionicons
                name="camera"
                size={22}
                color={isUploading ? (isDark ? '#666' : '#999') : isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
              <Text
                className={`font-medium ${isUploading ? (isDark ? 'text-gray-500' : 'text-gray-400') : isDark ? 'text-white' : 'text-black'}`}>
                Take Photo
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={chooseFromLibrary}
              disabled={isUploading}
              className={`h-14 flex-row items-center rounded-lg px-4 py-3 ${isUploading ? 'opacity-40' : ''}`}
              style={{ backgroundColor: isUploading ? colors.grey3 : colors.grey5 }}>
              <MaterialCommunityIcons
                name="image"
                size={22}
                color={isUploading ? (isDark ? '#666' : '#999') : isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
              <Text
                className={`font-medium ${isUploading ? (isDark ? 'text-gray-500' : 'text-gray-400') : isDark ? 'text-white' : 'text-black'}`}>
                Choose from Library
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={removePhoto}
              disabled={isUploading}
              className={`h-14 flex-row items-center rounded-lg px-4 py-3 ${isUploading ? 'opacity-40' : ''}`}
              style={{ backgroundColor: isUploading ? '#666' : '#ef4444' }}>
              <Ionicons
                name="trash"
                size={22}
                color={isUploading ? '#999' : '#fff'}
                style={{ marginRight: 12 }}
              />
              <Text className={`font-medium ${isUploading ? 'text-gray-400' : 'text-white'}`}>
                Remove Current Photo
              </Text>
            </TouchableOpacity>
          </View>
        </BottomSheetView>
      </BottomSheet>
    );
  }
);

export default PhotoUploadBottomSheet;
