import React, { useState } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { UserData } from '~/app/Auth/signup';
import { Button, ButtonText } from '@/components/ui/button';
import { FontAwesome5 } from '@expo/vector-icons';
import { Toast } from 'toastify-react-native';
import { UserService } from '~/services/UserService';
import { AuthService } from '~/services/AuthService';
import { useRouter } from 'expo-router';

interface InterestsStepProps {
  userData: UserData;
  updateUserData: (data: Partial<UserData>) => void;
  onNext: () => void;
}

export default function InterestsStep({ userData, updateUserData, onNext }: InterestsStepProps) {
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [selectedInterests, setSelectedInterests] = useState<string[]>(userData.interests || []);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const interestOptions = [
    { id: 'music', label: 'Music', icon: 'music' },
    { id: 'sports', label: 'Sports', icon: 'basketball-ball' },
    { id: 'food', label: 'Food & Dining', icon: 'utensils' },
    { id: 'art', label: 'Art & Design', icon: 'paint-brush' },
    { id: 'tech', label: 'Technology', icon: 'laptop-code' },
    { id: 'travel', label: 'Travel', icon: 'plane' },
    { id: 'fitness', label: 'Fitness', icon: 'dumbbell' },
    { id: 'gaming', label: 'Gaming', icon: 'gamepad' },
    { id: 'books', label: 'Books ', icon: 'book' },
    { id: 'finance', label: 'Finance', icon: 'chart-line' },
    { id: 'fashion', label: 'Fashion', icon: 'tshirt' },
    { id: 'photography', label: 'Photography', icon: 'camera' },
  ];

  const toggleInterest = (interestId: string) => {
    setSelectedInterests((currentInterests) => {
      if (currentInterests.includes(interestId)) {
        return currentInterests.filter((id) => id !== interestId);
      } else {
        return [...currentInterests, interestId];
      }
    });
  };

  /*   const handleLogin = async (data) => {
    try {
     

      return;
    } catch (error) {
      router.push('/Auth/login');
    } finally {
      setIsLoading(false);
    }
  }; */

  const handleContinue = async () => {
    if (selectedInterests.length === 0) return;

    setIsLoading(true);

    updateUserData({ interests: selectedInterests });

    try {
      await UserService.setupProfile({
        email: userData.email,
        phoneNumber: userData.phoneNumber,
        gender: userData.gender,
        interests: selectedInterests,
        eventPreferences: userData.eventPreferences,
        password: userData.password,
      });
      Toast.show({
        type: 'success',
        text1: 'Profile Setup Successful',
        text2: 'Your email or phone has been verified.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
      await onNext();
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Profile Setup Failed',
        text2: error.message,
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View className="flex-1 px-6 ">
      <Text className="mt-4 mb-2 text-xl font-bold" style={{ color: colors.foreground }}>
        What activities interest you?
      </Text>

      <ScrollView className="flex-1 mt-4 " showsVerticalScrollIndicator={false}>
        <View className="flex-row flex-wrap justify-between mb-6">
          {interestOptions.map((interest) => {
            const isSelected = selectedInterests.includes(interest.id);
            return (
              <Button
                key={interest.id}
                className={`mb-4 h-14 w-[48%] rounded-xl border px-3 ${
                  isSelected ? 'border-2' : 'border'
                }`}
                style={{
                  backgroundColor: colors.grey5,
                  borderColor: isSelected ? colors.primary : colors.grey5,
                }}
                onPress={() => toggleInterest(interest.id)}>
                <View className="flex-row items-center justify-center">
                  <FontAwesome5 name={interest.icon} size={14} color={colors.grey} />
                  <Text
                    className={`ml-2 font-medium text-sm `}
                    style={{ color: colors.grey }}
                    numberOfLines={1}>
                    {interest.label}
                  </Text>
                </View>
              </Button>
            );
          })}
        </View>
      </ScrollView>
      <View className="">
        <Text className="mt-5 mb-4 font-sans text-center text-gray-500">
          Selected: {selectedInterests.length} / {interestOptions.length}
        </Text>

        <Button
          className={`mb-8 mt-4 h-14 flex-row rounded-xl font-bold ${
            selectedInterests.length === 0
              ? isDark
                ? 'bg-gray-700'
                : 'bg-gray-300'
              : isDark
                ? 'bg-violet-700'
                : 'bg-violet-600'
          }`}
          onPress={handleContinue}
          isDisabled={selectedInterests.length === 0 || isLoading}>
          <ButtonText className="text-white">{isLoading ? 'Processing...' : 'Continue'}</ButtonText>
        </Button>
      </View>
    </View>
  );
}
