import React, { useState } from 'react';
import { View, Text, ScrollView, Image } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { UserData } from '~/app/Auth/signup';
import { Button, ButtonText } from '@/components/ui/button';
import { LinearGradient } from 'expo-linear-gradient';
import { FontAwesome5, MaterialIcons } from '@expo/vector-icons';

interface EventTypesStepProps {
  userData: UserData;
  updateUserData: (data: Partial<UserData>) => void;
  onNext: () => void;
}

export default function EventTypesStep({ userData, updateUserData, onNext }: EventTypesStepProps) {
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [selectedTypes, setSelectedTypes] = useState<string[]>(userData.eventPreferences || []);
  const [isLoading, setIsLoading] = useState(false);

  const eventTypes = [
    {
      id: 'business',
      label: 'Business',
      description: 'Networking, conferences, workshops',
      image: 'https://images.unsplash.com/photo-1565398305935-49e5dcc5c9f6?q=80&w=240&auto=format',
      icon: '',
    },
    {
      id: 'leisure',
      label: 'Leisure',
      description: 'Parties, social gatherings, hobbies',
      image: 'https://images.unsplash.com/photo-1517457373958-b7bdd4587205?q=80&w=240&auto=format',
      icon: '',
    },
    {
      id: 'entertainment',
      label: 'Entertainment',
      description: 'Concerts, shows, movies',
      image: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?q=80&w=240&auto=format',
      icon: '',
    },
    {
      id: 'education',
      label: 'Education',
      description: 'Courses, seminars, training',
      image: 'https://images.unsplash.com/photo-1524178232363-1fb2b075b655?q=80&w=240&auto=format',
      icon: '',
    },
    /*     {
      id: 'sports',
      label: 'Sports',
      description: 'Games, tournaments, fitness events',
      image: 'https://images.unsplash.com/photo-1471295253337-3ceaaedca402?q=80&w=240&auto=format',
    }, */
  ];

  const toggleEventType = (typeId: string) => {
    setSelectedTypes((current) => {
      if (current.includes(typeId)) {
        return current.filter((id) => id !== typeId);
      } else {
        return [...current, typeId];
      }
    });
  };

  const handleContinue = () => {
    if (selectedTypes.length === 0) return;

    setIsLoading(true);

    // Update user data
    updateUserData({ eventPreferences: selectedTypes });

    // Move to next step
    setTimeout(() => {
      setIsLoading(false);
      onNext();
    }, 300);
  };

  return (
    <View className="flex-1 px-6">
      <Text className="mb-2 mt-4 font-bold text-xl" style={{ color: colors.foreground }}>
        What events interest you?
      </Text>

      <ScrollView className="mt-4 flex-1" showsVerticalScrollIndicator={false}>
        <View className="">
          {eventTypes.map((type) => {
            const isSelected = selectedTypes.includes(type.id);
            return (
              <View key={type.id} className="mb-4">
                <Button
                  className={`h-20 overflow-hidden rounded-xl p-0 ${
                    isSelected ? 'border-2' : 'border'
                  }`}
                  style={{
                    backgroundColor: colors.grey5,
                    borderColor: isSelected ? colors.primary : colors.grey5,
                  }}
                  onPress={() => toggleEventType(type.id)}>
                  <View className="flex-1 flex-row">
                    <MaterialIcons name={type.icon} size={18} color={colors.grey} />
                    <View className="flex-1 justify-center p-4">
                      <Text
                        className={`font-medium text-base`}
                        style={{ color: colors.foreground }}>
                        {type.label}
                      </Text>
                      <Text
                        className={`text-sm ${isSelected ? 'text-violet-500' : ''}`}
                        style={{ color: colors.grey }}
                        numberOfLines={2}>
                        {type.description}
                      </Text>
                    </View>
                  </View>
                </Button>
              </View>
            );
          })}
        </View>
      </ScrollView>
      <View className="">
        <Text className="mb-4 mt-5 text-center font-sans text-gray-500 ">
          Selected: {selectedTypes.length} / {eventTypes.length}
        </Text>

        <Button
          className={`mb-8 mt-4 h-14 flex-row rounded-xl ${
            selectedTypes.length === 0
              ? isDark
                ? 'bg-gray-700'
                : 'bg-gray-300'
              : isDark
                ? 'bg-violet-700'
                : 'bg-violet-600'
          }`}
          onPress={handleContinue}
          isDisabled={selectedTypes.length === 0 || isLoading}>
          <ButtonText className="font-bold text-white">
            {isLoading ? 'Processing...' : 'Continue'}
          </ButtonText>
        </Button>
      </View>
    </View>
  );
}
