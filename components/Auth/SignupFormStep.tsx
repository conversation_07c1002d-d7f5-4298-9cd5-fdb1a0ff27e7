import React, { useRef } from 'react';
import {
  View,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { z } from 'zod';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FontAwesome5 } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import { UserData } from '~/app/Auth/signup';
import { AuthService } from '~/services/AuthService';

// Components
import {
  FormControl,
  FormControlError,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
} from '@/components/ui/form-control';
import { Input, InputField, InputSlot } from '~/components/ui/input';
import { Checkbox } from '~/components/ui/checkbox';
import { But<PERSON>, ButtonText } from '@/components/ui/button';
import { VStack } from '~/components/ui/vstack';
import { HStack } from '~/components/ui/hstack';
import { Toast } from 'toastify-react-native';

// Define validation schema
const signupSchema = z.object({
  fullName: z.string().min(2, 'Full name is required'),
  email: z.string().email('Please enter a valid email'),
  phoneNumber: z.string().regex(/^\d{9}$/, 'Phone number must be exactly 9 digits'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

type SignupFormData = z.infer<typeof signupSchema>;

interface SignupFormStepProps {
  userData: UserData;
  updateUserData: (data: Partial<UserData>) => void;
  onNext: () => void;
  router: any;
}

export default function SignupFormStep({
  userData,
  updateUserData,
  onNext,
  router,
}: SignupFormStepProps) {
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [showPassword, setShowPassword] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  /*   const [showConfirmPassword, setShowConfirmPassword] = React.useState(false); */

  const nameInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const phoneNumberInputRef = useRef<TextInput>(null);
  const passwordInputRef = useRef<TextInput>(null);
  const confirmPasswordInputRef = useRef<TextInput>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<SignupFormData>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      fullName: userData.fullName,
      email: userData.email,
      phoneNumber: userData.phoneNumber || '',
      password: userData.password,
      /*       confirmPassword: userData.password, */
      /*       termsAccepted: userData.termsAccepted, */
    },
  });

  const handleFormSubmit = async (data: SignupFormData) => {
    setIsLoading(true); // Start loading

    // Update userData with form values
    updateUserData({
      fullName: data.fullName,
      email: data.email,
      phoneNumber: data.phoneNumber,
      password: data.password,
    });

    try {
      console.log('initiate reg');
      await AuthService.initiateRegistration(data.email, data.phoneNumber);

      Toast.show({
        type: 'success',
        text1: 'Registration initiated successfully',
        text2: 'OTP was sent to your email',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
      // Move to next step
      onNext();
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error initiating registration',
        text2: error.message,
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsLoading(false); // Stop loading
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={100}
      style={{ flex: 1 }}>
      <ScrollView
        className="flex-1 px-6 pt-8"
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled">
        <Text className="mb-10 font-sans text-base " style={{ color: colors.grey }}>
          Enter your details to get started with us
        </Text>

        <VStack space="lg" className="mb-4">
          <FormControl isInvalid={!!errors.fullName}>
            <Controller
              name="fullName"
              control={control}
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  variant="outline"
                  className={`h-14 rounded-xl border-0  `}
                  style={{ backgroundColor: colors.grey5 }}>
                  <InputField
                    ref={nameInputRef}
                    placeholder="Enter your full name"
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    returnKeyType="next"
                    onSubmitEditing={() => emailInputRef.current?.focus()}
                    blurOnSubmit={false}
                    className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                    placeholderTextColor={isDark ? colors.grey : colors.grey}
                  />
                </Input>
              )}
            />
            {errors.fullName && (
              <FormControlError>
                <FormControlErrorText className="font-sans text-red-500">
                  {errors.fullName.message}
                </FormControlErrorText>
              </FormControlError>
            )}
          </FormControl>

          <FormControl isInvalid={!!errors.email}>
            <Controller
              name="email"
              control={control}
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  variant="outline"
                  className={`h-14 rounded-xl border-0  `}
                  style={{ backgroundColor: colors.grey5 }}>
                  <InputField
                    ref={emailInputRef}
                    placeholder="Enter your email"
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    autoCapitalize="none"
                    keyboardType="email-address"
                    returnKeyType="next"
                    onSubmitEditing={() => phoneNumberInputRef.current?.focus()}
                    blurOnSubmit={false}
                    className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                    placeholderTextColor={isDark ? colors.grey : colors.grey}
                  />
                </Input>
              )}
            />
            {errors.email && (
              <FormControlError>
                <FormControlErrorText className="font-sans text-red-500">
                  {errors.email.message}
                </FormControlErrorText>
              </FormControlError>
            )}
          </FormControl>

          <FormControl isInvalid={!!errors.phoneNumber}>
            <Controller
              name="phoneNumber"
              control={control}
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  variant="outline"
                  className={`h-14 rounded-xl border-0   `}
                  style={{ backgroundColor: colors.grey5 }}>
                  <InputField
                    ref={phoneNumberInputRef}
                    placeholder="Phone number (eg.718000000)"
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    keyboardType="phone-pad"
                    returnKeyType="next"
                    onSubmitEditing={() => passwordInputRef.current?.focus()}
                    blurOnSubmit={false}
                    className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                    placeholderTextColor={isDark ? colors.grey : colors.grey}
                  />
                </Input>
              )}
            />
            {errors.phoneNumber && (
              <FormControlError>
                <FormControlErrorText className="font-sans text-red-500">
                  {errors.phoneNumber.message}
                </FormControlErrorText>
              </FormControlError>
            )}
          </FormControl>

          <FormControl isInvalid={!!errors.password}>
            <Controller
              name="password"
              control={control}
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  variant="outline"
                  className={`h-14 rounded-xl border-0  `}
                  style={{ backgroundColor: colors.grey5 }}>
                  <InputField
                    ref={passwordInputRef}
                    placeholder="Create a strong password"
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    secureTextEntry={!showPassword}
                    returnKeyType="next"
                    onSubmitEditing={() => confirmPasswordInputRef.current?.focus()}
                    className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                    placeholderTextColor={isDark ? colors.grey : colors.grey}
                  />
                  <InputSlot className="pr-3">
                    <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
                      <FontAwesome5
                        name={showPassword ? 'eye-slash' : 'eye'}
                        size={18}
                        color={isDark ? '#9ca3af' : '#6b7280'}
                      />
                    </TouchableOpacity>
                  </InputSlot>
                </Input>
              )}
            />
            {errors.password && (
              <FormControlError>
                <FormControlErrorText className="font-sans text-red-500">
                  {errors.password.message}
                </FormControlErrorText>
              </FormControlError>
            )}
          </FormControl>

          <FormControl isInvalid={!!errors.termsAccepted}>
            <HStack space="sm" className="items-start">
              <Text className="flex-1 font-sans" style={{ color: colors.foreground }}>
                By Continuing, I acknowledge that I have read and agree to the
                <Text
                  className="font-bold "
                  style={{ color: colors.primary }}
                  onPress={() => router.push('/Auth/termsAndConditions')}>
                  {' '}
                  Terms and Conditions
                </Text>
              </Text>
            </HStack>
            {errors.termsAccepted && (
              <FormControlError>
                <FormControlErrorText className="ml-8 font-sans text-red-500">
                  {errors.termsAccepted.message}
                </FormControlErrorText>
              </FormControlError>
            )}
          </FormControl>
        </VStack>

        <Button
          className={`mb-10 mt-6 h-14 flex-row rounded-xl ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
          onPress={handleSubmit(handleFormSubmit)}
          disabled={isLoading}>
          <ButtonText className="font-bold text-white">
            {isLoading ? 'Loading...' : 'Continue'}
          </ButtonText>
        </Button>

        <HStack className="mb-10 justify-center">
          <Text className="font-sans text-sm" style={{ color: colors.foreground }}>
            Already have an account?
          </Text>
          <TouchableOpacity onPress={() => router.push('/Auth/login')}>
            <Text className="ml-1 font-bold text-sm" style={{ color: colors.primary }}>
              Login
            </Text>
          </TouchableOpacity>
        </HStack>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
