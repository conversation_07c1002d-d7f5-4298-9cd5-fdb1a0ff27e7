import React, { useState, useRef } from 'react';
import { View, Text, TextInput, KeyboardAvoidingView, Platform } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { UserData } from '~/app/Auth/signup';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FontAwesome5 } from '@expo/vector-icons';

import {
  FormControl,
  FormControlError,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
} from '@/components/ui/form-control';
import { Input, InputField } from '~/components/ui/input';
import { Button, ButtonText } from '@/components/ui/button';
import { VStack } from '~/components/ui/vstack';

// Define validation schema
const usernameSchema = z.object({
  username: z
    .string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username must be less than 20 characters')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),
});

type UsernameFormData = z.infer<typeof usernameSchema>;

interface UsernameStepProps {
  userData: UserData;
  updateUserData: (data: Partial<UserData>) => void;
  onNext: () => void;
}

export default function UsernameStep({ userData, updateUserData, onNext }: UsernameStepProps) {
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isChecking, setIsChecking] = useState(false);

  const usernameInputRef = useRef<TextInput>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<UsernameFormData>({
    resolver: zodResolver(usernameSchema),
    defaultValues: {
      username: userData.username || '',
    },
  });

  const handleFormSubmit = (data: UsernameFormData) => {
    setIsChecking(true);

    // Simulate checking username availability
    setTimeout(() => {
      // Update userData with the chosen username
      updateUserData({ username: data.username });
      setIsChecking(false);
      onNext();
    }, 1000);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 20}
      className="flex-1 px-6 pt-8">
      <View className="mb-6 rounded-lg bg-yellow-100 p-4 ">
        <Text className="text-sm text-yellow-800">
          <Text className="font-bold text-base">Note:</Text> You need to complete your profile setup
          to use all app features. If you exit now, you'll need to complete this setup the next time
          you log in.
        </Text>
      </View>

      <VStack space="lg" className="mt-5">
        <FormControl isInvalid={!!errors.username}>
          <Controller
            name="username"
            control={control}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input
                variant="outline"
                className={`h-14 rounded-xl border-0 `}
                style={{ backgroundColor: colors.grey5 }}>
                <InputField
                  ref={usernameInputRef}
                  placeholder="Enter username"
                  onBlur={onBlur}
                  onChangeText={onChange}
                  value={value}
                  autoCapitalize="none"
                  returnKeyType="done"
                  onSubmitEditing={handleSubmit(handleFormSubmit)}
                  className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                  placeholderTextColor={isDark ? colors.grey : colors.grey}
                />
              </Input>
            )}
          />
          {errors.username && (
            <FormControlError>
              <FormControlErrorText className="text-red-500">
                {errors.username.message}
              </FormControlErrorText>
            </FormControlError>
          )}
        </FormControl>
      </VStack>

      <Text className="mt-2 font-sans text-sm" style={{ color: colors.grey }}>
        This will be your public username that other users can find you with.
      </Text>

      <Button
        className={`mt-8 h-14 flex-row rounded-xl ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
        onPress={handleSubmit(handleFormSubmit)}
        isDisabled={isChecking}>
        <ButtonText className="font-bold text-white">
          {isChecking ? 'Checking availability...' : 'Continue'}
        </ButtonText>
      </Button>
    </KeyboardAvoidingView>
  );
}
