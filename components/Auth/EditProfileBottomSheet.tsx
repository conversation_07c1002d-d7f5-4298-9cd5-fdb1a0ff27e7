import React, { useRef, forwardRef, useImperative<PERSON>andle, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
} from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useKeyboardHandler } from 'react-native-keyboard-controller'; // Added import

import BottomSheet, { BottomSheetView, BottomSheetModal } from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '../RenderBackdrop';

// UI Components
import {
  FormControl,
  FormControlError,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
} from '@/components/ui/form-control';
import { Input, InputField, InputSlot } from '~/components/ui/input';
import { Button, ButtonText } from '@/components/ui/button';

export interface EditProfileBottomSheetProps {
  title: string;
  currentValue: string;
  fieldName: string;
  multiline?: boolean;
  isPassword?: boolean;
  isEmail?: boolean;
  isPhone?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  maxLength?: number;
  onSave: (value: string) => void;
}

export interface BottomSheetHandle {
  present: () => void;
  dismiss: () => void;
}

const EditProfileBottomSheet = forwardRef<BottomSheetHandle, EditProfileBottomSheetProps>(
  (
    {
      title,
      currentValue,
      fieldName,
      multiline = false,
      isPassword = false,
      isEmail = false,
      isPhone = false,
      keyboardType = 'default',
      maxLength,
      onSave,
    },
    ref
  ) => {
    const bottomSheetModalRef = useRef<BottomSheetModal>(null);
    const { colors, colorScheme } = useColorScheme();
    const isDark = colorScheme === 'dark';

    // Create a schema dynamically based on field type
    const createValidationSchema = () => {
      let schema = z.string().min(1, `${fieldName} cannot be empty`);

      if (isEmail) {
        schema = schema.email('Please enter a valid email address');
      }

      if (isPhone) {
        schema = schema.refine(
          (value) => /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/.test(value),
          { message: 'Please enter a valid phone number' }
        );
      }

      if (isPassword) {
        schema = schema
          .min(8, 'Password must be at least 8 characters long')
          .refine((value) => /\d/.test(value), {
            message: 'Password must include at least one number',
          });
      }

      return z.object({
        value: schema,
      });
    };

    // Create form with validation
    const {
      control,
      handleSubmit,
      reset,
      formState: { errors },
    } = useForm({
      resolver: zodResolver(createValidationSchema()),
      defaultValues: {
        value: currentValue,
      },
    });

    useImperativeHandle(ref, () => ({
      present: () => {
        console.log('Attempting to present bottom sheet');
        reset({ value: currentValue });
        if (bottomSheetModalRef.current) {
          bottomSheetModalRef.current.expand();
          console.log('Bottom sheet present method called');
        } else {
          console.log('Bottom sheet ref is null');
        }
      },
      dismiss: () => {
        if (bottomSheetModalRef.current) {
          bottomSheetModalRef.current.close();
          console.log('Bottom sheet dismiss method called');
        } else {
          console.log('Bottom sheet ref is null when dismissing');
        }
      },
    }));

    const handleSave = (data: { value: string }) => {
      console.log(`Saving value: ${data.value}`);
      onSave(data.value);
      bottomSheetModalRef.current?.close();
    };

    const [snapPoints, setSnapPoints] = React.useState([400]); // Default snap point in pixels

    useEffect(() => {
      const onKeyboardShow = (event: KeyboardEvent) => {
        const keyboardHeight = event.endCoordinates.height;
        setSnapPoints([300 + keyboardHeight]); // Adjust snap point based on keyboard height
      };

      const onKeyboardHide = () => {
        setSnapPoints([300]); // Reset to default snap point
      };

      const keyboardShowListener = Keyboard.addListener('keyboardDidShow', onKeyboardShow);
      const keyboardHideListener = Keyboard.addListener('keyboardDidHide', onKeyboardHide);

      return () => {
        keyboardShowListener.remove();
        keyboardHideListener.remove();
      };
    }, []);

    return (
      <BottomSheet
        ref={bottomSheetModalRef}
        index={-1}
        snapPoints={snapPoints} // Use numeric snap points
        backdropComponent={RenderBackdrop}
        enablePanDownToClose={true}
        enableDynamicSizing={true}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#555' : '#999',
          width: 40,
        }}
        onChange={(index) => {
          console.log(`Bottom sheet changed to index: ${index}`);
        }}>
        <BottomSheetView className="flex-1 px-5 pt-2">
          <View className="mb-5 flex-row items-center justify-between">
            <Text className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
              {title}
            </Text>
            <View className="flex-row">
              <TouchableOpacity
                className="p-2"
                onPress={() => {
                  bottomSheetModalRef.current?.close();
                  Keyboard.dismiss();
                }}>
                <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
              </TouchableOpacity>
            </View>
          </View>

          <FormControl isInvalid={!!errors.value} className="mb-4">
            <FormControlLabel>
              <FormControlLabelText
                className={`mb-1.5 text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                {fieldName}
              </FormControlLabelText>
            </FormControlLabel>
            <Controller
              name="value"
              control={control}
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  variant="outline"
                  size="md"
                  className={`rounded-lg border-0 ${multiline ? 'h-24' : 'h-14'}`}
                  style={{ backgroundColor: colors.grey5 }}>
                  <InputField
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    secureTextEntry={isPassword}
                    multiline={multiline}
                    keyboardType={keyboardType}
                    maxLength={maxLength}
                    className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                    placeholderTextColor={isDark ? colors.grey : colors.grey}
                    autoCapitalize={isEmail ? 'none' : 'sentences'}
                    autoCorrect={!isEmail && !isPassword && !isPhone}
                    textAlignVertical={multiline ? 'top' : 'center'}
                  />
                  {isPassword && (
                    <InputSlot className="pr-3">
                      <TouchableOpacity>
                        <Ionicons name="eye" size={18} color={isDark ? '#9ca3af' : '#6b7280'} />
                      </TouchableOpacity>
                    </InputSlot>
                  )}
                </Input>
              )}
            />
            <FormControlError>
              <FormControlErrorText className="mt-1 text-sm text-red-500">
                {errors.value?.message}
              </FormControlErrorText>
            </FormControlError>
          </FormControl>

          <Button
            onPress={handleSubmit(handleSave)}
            className="mt-2 h-14 items-center rounded-lg bg-violet-600 py-3.5">
            <ButtonText className="text-base font-semibold text-white">Save</ButtonText>
          </Button>
        </BottomSheetView>
      </BottomSheet>
    );
  }
);

export default EditProfileBottomSheet;
