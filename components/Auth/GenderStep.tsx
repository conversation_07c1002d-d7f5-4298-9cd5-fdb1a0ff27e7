import React, { useState } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { UserData } from '~/app/Auth/signup';
import { Button, ButtonText } from '@/components/ui/button';
import { VStack } from '~/components/ui/vstack';
import { FontAwesome5 } from '@expo/vector-icons';

interface GenderStepProps {
  userData: UserData;
  updateUserData: (data: Partial<UserData>) => void;
  onNext: () => void;
}

export default function GenderStep({ userData, updateUserData, onNext }: GenderStepProps) {
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [selectedGender, setSelectedGender] = useState(userData.gender || '');
  const [isLoading, setIsLoading] = useState(false);

  const genderOptions = [
    { id: 'male', label: 'Male', icon: 'male' },
    { id: 'female', label: 'Female', icon: 'female' },
    { id: 'prefer-not', label: 'Prefer not to say', icon: 'user-secret' },
  ];

  const handleContinue = () => {
    if (!selectedGender) return;

    setIsLoading(true);

    // Update user data
    updateUserData({ gender: selectedGender });

    // Simulate processing
    setTimeout(() => {
      setIsLoading(false);
      onNext();
    }, 300);
  };

  return (
    <ScrollView className="flex-1 px-6 pt-8" showsVerticalScrollIndicator={false}>
      <VStack space="md" className="mb-6">
        {genderOptions.map((option) => (
          <Button
            key={option.id}
            variant="outline"
            style={{
              backgroundColor: colors.grey5,
              borderColor: selectedGender === option.id ? colors.primary : colors.grey5,
            }}
            className={`h-14 flex-row justify-start rounded-xl border px-4 ${
              selectedGender === option.id ? 'border-2' : 'border'
            }`}
            onPress={() => setSelectedGender(option.id)}>
            <FontAwesome5 name={option.icon} size={18} color={colors.grey} />
            <Text className={`ml-4 font-medium`} style={{ color: colors.grey }}>
              {option.label}
            </Text>
          </Button>
        ))}
      </VStack>

      <Button
        className={`mb-8 mt-4 h-14 flex-row rounded-xl font-bold ${
          !selectedGender
            ? isDark
              ? 'bg-gray-700'
              : 'bg-gray-300'
            : isDark
              ? 'bg-violet-700'
              : 'bg-violet-600'
        }`}
        onPress={handleContinue}
        isDisabled={!selectedGender || isLoading}>
        <ButtonText className="text-white">{isLoading ? 'Processing...' : 'Continue'}</ButtonText>
      </Button>
    </ScrollView>
  );
}
