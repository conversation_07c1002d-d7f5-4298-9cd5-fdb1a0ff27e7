import { VariantProps } from 'class-variance-authority';
import * as React from 'react';
import { memo } from 'react';
import { UITextView } from 'react-native-uitextview';

import { Text as OriginalText, TextClassContext, textVariants } from './Text';

// Memoized Text component to prevent unnecessary rerenders
const Text = memo(
  React.forwardRef<
    React.ComponentRef<typeof UITextView>,
    React.ComponentPropsWithoutRef<typeof UITextView> & VariantProps<typeof textVariants>
  >(function Text(props, ref) {
    return <OriginalText ref={ref} {...props} />;
  })
);

Text.displayName = 'MemoizedText';

export { Text, TextClassContext, textVariants };
