import React from 'react';
import { View, ViewProps } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';

export interface ThemedViewProps extends ViewProps {
  variant?: 'primary' | 'card' | 'background';
  className?: string;
}

export const ThemedView = React.forwardRef<View, ThemedViewProps>(
  ({ variant = 'background', style, className = '', children, ...props }, ref) => {
    const { theme, isDark } = useColorScheme();

    // Map variant to theme color
    let backgroundColor;
    switch (variant) {
      case 'primary':
        backgroundColor = theme.colors.primary;
        break;
      case 'card':
        backgroundColor = theme.colors.card;
        break;
      case 'background':
      default:
        backgroundColor = theme.colors.background;
        break;
    }

    // Build Tailwind classes based on variant
    let variantClass = '';
    switch (variant) {
      case 'primary':
        variantClass = isDark ? 'bg-dark-primary' : 'bg-light-primary';
        break;
      case 'card':
        variantClass = isDark ? 'bg-dark-card' : 'bg-light-card';
        break;
      case 'background':
      default:
        variantClass = isDark ? 'bg-dark-background' : 'bg-light-background';
        break;
    }

    const tailwindClasses = `${className} ${variantClass}`;

    return (
      <View
        ref={ref}
        className={tailwindClasses}
        style={[
          {
            backgroundColor,
          },
          style,
        ]}
        {...props}
      >
        {children}
      </View>
    );
  }
);

ThemedView.displayName = 'ThemedView';
