import React from 'react';
import { Text as RNText, TextProps as RNTextProps } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';

export interface ThemedTextProps extends RNTextProps {
  variant?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl';
  weight?: 'regular' | 'medium' | 'bold';
  color?: 'primary' | 'secondary' | 'accent' | 'error' | 'success' | 'warning' | 'info';
  className?: string;
}

export const ThemedText = React.forwardRef<RNText, ThemedTextProps>(
  ({ variant = 'base', weight = 'regular', color, style, className = '', children, ...props }, ref) => {
    const { theme, isDark, fonts, fontSizes } = useColorScheme();

    // Map weight to font family
    const fontFamily = {
      regular: fonts.sans,
      medium: fonts.medium,
      bold: fonts.bold,
    }[weight];

    // Map variant to font size and line height
    const variantStyle = fontSizes[variant];

    // Map color to theme color
    let textColor = theme.colors.text;
    if (color) {
      switch (color) {
        case 'primary':
          textColor = theme.colors.primary;
          break;
        case 'secondary':
          textColor = theme.colors.textSecondary;
          break;
        case 'accent':
          textColor = theme.colors.accent;
          break;
        case 'error':
          textColor = theme.colors.error;
          break;
        case 'success':
          textColor = theme.colors.success;
          break;
        case 'warning':
          textColor = theme.colors.warning;
          break;
        case 'info':
          textColor = theme.colors.info;
          break;
      }
    }

    // Build Tailwind classes
    const tailwindClasses = `${className} ${isDark ? 'dark:text-dark-text' : 'text-light-text'}`;

    return (
      <RNText
        ref={ref}
        className={tailwindClasses}
        style={[
          {
            color: textColor,
            fontFamily,
            fontSize: variantStyle.fontSize,
            lineHeight: variantStyle.lineHeight,
          },
          style,
        ]}
        {...props}
      >
        {children}
      </RNText>
    );
  }
);

ThemedText.displayName = 'ThemedText';
