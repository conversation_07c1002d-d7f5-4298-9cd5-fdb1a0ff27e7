'use client';
import React, { memo } from 'react';
import { Button as OriginalButton, ButtonText as OriginalButtonText, ButtonIcon, ButtonSpinner, ButtonGroup } from '../button';
import type { VariantProps } from '@gluestack-ui/nativewind-utils';
import { buttonStyle } from '../button/styles';

// Memoized Button component to prevent unnecessary rerenders
const Button = memo(React.forwardRef<
  React.ComponentRef<typeof OriginalButton>,
  React.ComponentPropsWithoutRef<typeof OriginalButton>
>(function Button(props, ref) {
  return <OriginalButton ref={ref} {...props} />;
}));

// Memoized ButtonText component to prevent unnecessary rerenders
const ButtonText = memo(React.forwardRef<
  React.ComponentRef<typeof OriginalButtonText>,
  React.ComponentPropsWithoutRef<typeof OriginalButtonText>
>(function ButtonText(props, ref) {
  return <OriginalButtonText ref={ref} {...props} />;
}));

Button.displayName = 'MemoizedButton';
ButtonText.displayName = 'MemoizedButtonText';

export { Button, ButtonText, ButtonIcon, ButtonSpinner, ButtonGroup };
