import React from 'react';
import { View } from 'react-native';
import { ThemedText } from './themed-text';
import { ThemedView } from './themed-view';
import { ThemedPressable } from './themed-pressable';
import { useColorScheme } from '~/lib/useColorScheme';

export const ThemeExample = () => {
  const { isDark, toggleColorScheme } = useColorScheme();

  return (
    <ThemedView variant="background" className="flex-1 p-4">
      <ThemedText variant="2xl" weight="bold" className="mb-4">
        Theme Example
      </ThemedText>

      <ThemedText variant="lg" weight="medium" className="mb-2">
        Current Theme: {isDark ? 'Dark' : 'Light'}
      </ThemedText>

      <ThemedPressable
        variant="primary"
        className="mb-4 items-center rounded-lg p-3"
        onPress={toggleColorScheme}
      >
        <ThemedText weight="bold" color="secondary">
          Toggle Theme
        </ThemedText>
      </ThemedPressable>

      <ThemedView variant="card" className="mb-4 rounded-lg p-4">
        <ThemedText variant="lg" weight="bold" className="mb-2">
          Card Example
        </ThemedText>
        <ThemedText>This is a card with themed text.</ThemedText>
      </ThemedView>

      <ThemedText variant="lg" weight="bold" className="mb-2">
        Text Variants
      </ThemedText>

      <ThemedText variant="xs" className="mb-1">
        Extra Small Text
      </ThemedText>
      <ThemedText variant="sm" className="mb-1">
        Small Text
      </ThemedText>
      <ThemedText variant="base" className="mb-1">
        Base Text
      </ThemedText>
      <ThemedText variant="lg" className="mb-1">
        Large Text
      </ThemedText>
      <ThemedText variant="xl" className="mb-1">
        Extra Large Text
      </ThemedText>
      <ThemedText variant="2xl" className="mb-4">
        2XL Text
      </ThemedText>

      <ThemedText variant="lg" weight="bold" className="mb-2">
        Text Weights
      </ThemedText>

      <ThemedText weight="regular" className="mb-1">
        Regular Text
      </ThemedText>
      <ThemedText weight="medium" className="mb-1">
        Medium Text
      </ThemedText>
      <ThemedText weight="bold" className="mb-4">
        Bold Text
      </ThemedText>

      <ThemedText variant="lg" weight="bold" className="mb-2">
        Text Colors
      </ThemedText>

      <ThemedText color="primary" className="mb-1">
        Primary Color
      </ThemedText>
      <ThemedText color="secondary" className="mb-1">
        Secondary Color
      </ThemedText>
      <ThemedText color="accent" className="mb-1">
        Accent Color
      </ThemedText>
      <ThemedText color="error" className="mb-1">
        Error Color
      </ThemedText>
      <ThemedText color="success" className="mb-1">
        Success Color
      </ThemedText>
      <ThemedText color="warning" className="mb-1">
        Warning Color
      </ThemedText>
      <ThemedText color="info" className="mb-1">
        Info Color
      </ThemedText>
    </ThemedView>
  );
};
