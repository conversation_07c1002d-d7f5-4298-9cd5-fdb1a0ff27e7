import React from 'react';
import { Pressable, PressableProps } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';

export interface ThemedPressableProps extends PressableProps {
  variant?: 'primary' | 'secondary' | 'accent' | 'card' | 'transparent';
  className?: string;
}

export const ThemedPressable = React.forwardRef<Pressable, ThemedPressableProps>(
  ({ variant = 'transparent', style, className = '', children, ...props }, ref) => {
    const { theme, isDark } = useColorScheme();

    // Map variant to theme color
    let backgroundColor;
    switch (variant) {
      case 'primary':
        backgroundColor = theme.colors.primary;
        break;
      case 'secondary':
        backgroundColor = theme.colors.textSecondary;
        break;
      case 'accent':
        backgroundColor = theme.colors.accent;
        break;
      case 'card':
        backgroundColor = theme.colors.card;
        break;
      case 'transparent':
      default:
        backgroundColor = 'transparent';
        break;
    }

    // Build Tailwind classes based on variant
    let variantClass = '';
    switch (variant) {
      case 'primary':
        variantClass = isDark ? 'bg-dark-primary active:bg-dark-accent' : 'bg-light-primary active:bg-light-accent';
        break;
      case 'secondary':
        variantClass = isDark ? 'bg-dark-card active:bg-dark-accent' : 'bg-light-card active:bg-light-accent';
        break;
      case 'accent':
        variantClass = isDark ? 'bg-dark-accent active:bg-dark-primary' : 'bg-light-accent active:bg-light-primary';
        break;
      case 'card':
        variantClass = isDark ? 'bg-dark-card active:bg-dark-accent' : 'bg-light-card active:bg-light-accent';
        break;
      case 'transparent':
      default:
        variantClass = isDark ? 'active:bg-dark-card' : 'active:bg-light-card';
        break;
    }

    const tailwindClasses = `${className} ${variantClass}`;

    return (
      <Pressable
        ref={ref}
        className={tailwindClasses}
        style={[
          {
            backgroundColor,
          },
          style,
        ]}
        {...props}
      >
        {children}
      </Pressable>
    );
  }
);

ThemedPressable.displayName = 'ThemedPressable';
