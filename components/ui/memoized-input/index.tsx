'use client';
import React, { memo } from 'react';
import { Input as OriginalInput, InputField as OriginalInputField, InputSlot as OriginalInputSlot } from '../input';

// Memoized Input component to prevent unnecessary rerenders
const Input = memo(React.forwardRef<
  React.ComponentRef<typeof OriginalInput>,
  React.ComponentPropsWithoutRef<typeof OriginalInput>
>(function Input(props, ref) {
  return <OriginalInput ref={ref} {...props} />;
}));

// Memoized InputField component to prevent unnecessary rerenders
const InputField = memo(React.forwardRef<
  React.ComponentRef<typeof OriginalInputField>,
  React.ComponentPropsWithoutRef<typeof OriginalInputField>
>(function InputField(props, ref) {
  return <OriginalInputField ref={ref} {...props} />;
}));

// Memoized InputSlot component to prevent unnecessary rerenders
const InputSlot = memo(React.forwardRef<
  React.ComponentRef<typeof OriginalInputSlot>,
  React.ComponentPropsWithoutRef<typeof OriginalInputSlot>
>(function InputSlot(props, ref) {
  return <OriginalInputSlot ref={ref} {...props} />;
}));

Input.displayName = 'MemoizedInput';
InputField.displayName = 'MemoizedInputField';
InputSlot.displayName = 'MemoizedInputSlot';

export { Input, InputField, InputSlot };
