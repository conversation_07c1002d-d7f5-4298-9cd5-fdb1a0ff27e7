import { Ionicons } from '@expo/vector-icons';
import React, { useState, useRef, RefObject } from 'react';
import { View, Text, TouchableOpacity, Image, Alert, FlatList, Dimensions } from 'react-native';
import { Toast } from 'toastify-react-native';

import PhotoViewModal from './PhotoViewModal';
import ProfilePhotoUploadBottomSheet, {
  ProfilePhotoUploadBottomSheetHandle,
} from './ProfilePhotoUploadBottomSheet';
import { useColorScheme } from '~/lib/useColorScheme';

interface ProfilePhoto {
  id: string;
  secureUrl: string;
  publicId?: string;
}

interface ProfilePhotosGridProps {
  userId: string;
  photos: ProfilePhoto[];
  onPhotosUpdate: (photos: ProfilePhoto[]) => void;
  onOpenUploadSheet: () => void;
}

const { width } = Dimensions.get('window');
const GRID_ITEM_SIZE = (width - 48) / 3; // 3 columns with padding

export default function ProfilePhotosGrid({
  userId,
  photos,
  onPhotosUpdate,
  onOpenUploadSheet,
}: ProfilePhotosGridProps) {
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [selectedPhoto, setSelectedPhoto] = useState<ProfilePhoto | null>(null);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);

  const showImageOptions = () => {
    onOpenUploadSheet();
  };

  const deletePhoto = (photo: ProfilePhoto) => {
    Alert.alert(
      'Delete Photo',
      'Are you sure you want to delete this photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            const updatedPhotos = photos.filter((p) => p.id !== photo.id);
            onPhotosUpdate(updatedPhotos);
            Toast.show({
              type: 'success',
              text1: 'Photo Deleted',
              text2: 'Photo has been removed from your profile.',
              position: 'bottom',
              theme: isDark ? 'dark' : 'light',
              backgroundColor: colors.background,
              autoHide: true,
            });
          },
        },
      ],
      { cancelable: true }
    );
  };

  const viewPhoto = (photo: ProfilePhoto) => {
    setSelectedPhoto(photo);
    setIsViewModalVisible(true);
  };

  const renderPhotoItem = ({ item }: { item: ProfilePhoto }) => (
    <TouchableOpacity
      onPress={() => viewPhoto(item)}
      onLongPress={() => deletePhoto(item)}
      className="relative mb-2"
      style={{ width: GRID_ITEM_SIZE, height: GRID_ITEM_SIZE }}>
      <Image
        source={{ uri: item.secureUrl }}
        className="w-full h-full rounded-lg"
        resizeMode="cover"
      />
      <TouchableOpacity
        className="absolute p-1 bg-red-500 rounded-full right-1 top-1"
        onPress={() => deletePhoto(item)}>
        <Ionicons name="close" size={12} color="#fff" />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const renderAddButton = () => (
    <TouchableOpacity
      onPress={showImageOptions}
      className="items-center justify-center border-2 border-dashed rounded-lg"
      style={{
        width: GRID_ITEM_SIZE,
        height: GRID_ITEM_SIZE,
        borderColor: colors.grey,
        backgroundColor: colors.grey5,
      }}>
      <Ionicons name="add" size={32} color={colors.grey} />
      <Text className="mt-1 text-xs" style={{ color: colors.grey }}>
        Add Photo
      </Text>
    </TouchableOpacity>
  );

  const gridData = [...photos, { id: 'add-button', secureUrl: '', isAddButton: true }];

  return (
    <View className="mb-6">
      <Text className={`mb-3 text-lg font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
        Profile Photos
      </Text>

      <FlatList
        data={gridData}
        renderItem={({ item }) => {
          if ('isAddButton' in item) {
            return renderAddButton();
          }
          return renderPhotoItem({ item: item as ProfilePhoto });
        }}
        keyExtractor={(item) => item.id}
        numColumns={3}
        columnWrapperStyle={{ justifyContent: 'space-between' }}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
      />

      {/* Photo View Modal */}
      <PhotoViewModal
        visible={isViewModalVisible}
        photo={selectedPhoto}
        onClose={() => setIsViewModalVisible(false)}
      />

      {/*   
      <ProfilePhotoUploadBottomSheet
        ref={uploadBottomSheetRef}
        userId={userId}
        onPhotoUploaded={onPhotosUpdate}
      /> */}
    </View>
  );
}
