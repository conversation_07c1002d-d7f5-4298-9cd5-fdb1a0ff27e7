import React, { useRef, forwardRef, useImperativeHandle, useState } from 'react';
import { View, Text, TouchableOpacity, Alert, ActivityIndicator, Platform } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetView, BottomSheetModal } from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '../RenderBackdrop';
import ImagePicker from 'react-native-image-crop-picker';
import { FileService } from '~/services/FileService';
import { UserStore } from '~/store/store';
import { Toast } from 'toastify-react-native';

interface ProfilePhoto {
  id: string;
  secureUrl: string;
  publicId?: string;
}

export interface ProfilePhotoUploadBottomSheetProps {
  userId: string;
  onPhotoUploaded: (photos: ProfilePhoto[]) => void;
}

export interface ProfilePhotoUploadBottomSheetHandle {
  present: () => void;
  dismiss: () => void;
}

const ProfilePhotoUploadBottomSheet = forwardRef<
  ProfilePhotoUploadBottomSheetHandle,
  ProfilePhotoUploadBottomSheetProps
>(({ userId, onPhotoUploaded }, ref) => {
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const { colorScheme, colors } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isUploading, setIsUploading] = useState(false);

  useImperativeHandle(ref, () => ({
    present: () => {
      bottomSheetModalRef.current?.expand();
    },
    dismiss: () => bottomSheetModalRef.current?.close(),
  }));

  const uploadImageFile = async (uri: string) => {
    try {
      setIsUploading(true);

      // Create file object from URI
      const filename = uri.split('/').pop() || 'image.jpg';
      const fileType = filename.split('.').pop() || 'jpg';

      const file = {
        uri,
        type: `image/${fileType}`,
        name: filename,
      } as any;

      const response = await FileService.uploadManyImages([file], userId, 'PROFILE_UPLOAD');

      // Update user store with new data
      (UserStore.getState() as { setUser: (data: any) => void }).setUser(response.body);

      // Update local photos state with the latest data from the response
      onPhotoUploaded(response.body.profileUploads || []);

      bottomSheetModalRef.current?.close();
      Toast.show({
        type: 'success',
        text1: 'Photo Added',
        text2: 'Your photo has been added to your profile.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      Toast.show({
        type: 'error',
        text1: 'Upload Failed',
        text2: 'Failed to upload photo. Please try again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsUploading(false);
    }
  };

  const takePhoto = async () => {
    try {
      const image = await ImagePicker.openCamera({
        width: 400,
        height: 400,
        cropping: true,
        cropperCircleOverlay: false,
        compressImageQuality: 0.8,
        freeStyleCropEnabled: true,
        includeBase64: false,
      });

      await uploadImageFile(image.path);
    } catch (error: any) {
      if (error.code !== 'E_PICKER_CANCELLED') {
        Alert.alert('Permission required', 'Camera permission is required to take photos');
      }
    }
  };

  const chooseFromLibrary = async () => {
    try {
      const image = await ImagePicker.openPicker({
        width: 400,
        height: 400,
        cropping: true,
        cropperCircleOverlay: false,
        compressImageQuality: 0.8,
        freeStyleCropEnabled: true,
        includeBase64: false,
        mediaType: 'photo',
        cropperStatusBarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarWidgetColor: Platform.OS === 'android' ? colors.foreground : undefined,
        cropperActiveWidgetColor: Platform.OS === 'android' ? colors.primary : undefined,
        showCropGuidelines: Platform.OS === 'android' ? true : undefined,
        showCropFrame: Platform.OS === 'android' ? true : undefined,
        hideBottomControls: Platform.OS === 'android' ? false : undefined,
        enableRotationGesture: Platform.OS === 'android' ? true : undefined,
        disableCropperColorSetters: Platform.OS === 'android' ? false : undefined,
        // iOS-specific configurations
        cropperChooseColor: Platform.OS === 'ios' ? colors.primary : undefined,
        cropperCancelColor: Platform.OS === 'ios' ? colors.foreground : undefined,
        avoidEmptySpaceAroundImage: Platform.OS === 'ios' ? true : undefined,
        cropperToolbarTitle: 'Edit Photo',
      });

      await uploadImageFile(image.path);
    } catch (error: any) {
      if (error.code !== 'E_PICKER_CANCELLED') {
        Alert.alert('Permission required', 'Gallery permission is required to select photos');
      }
    }
  };

  return (
    <BottomSheet
      ref={bottomSheetModalRef}
      index={-1}
      snapPoints={['50%']}
      backdropComponent={RenderBackdrop}
      backgroundStyle={{
        backgroundColor: colors.background,
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
      }}
      handleIndicatorStyle={{
        backgroundColor: isDark ? '#555' : '#999',
        width: 40,
      }}>
      <BottomSheetView className="flex-1 px-5 pt-2">
        <View className="flex-row items-center justify-between mb-5">
          <Text className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
            Add Profile Photo
          </Text>
          <TouchableOpacity className="p-2" onPress={() => bottomSheetModalRef.current?.close()}>
            <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
          </TouchableOpacity>
        </View>

        <View className="flex gap-3">
          <TouchableOpacity
            onPress={takePhoto}
            disabled={isUploading}
            className={`h-14 flex-row items-center rounded-lg px-4 py-3 ${isUploading ? 'opacity-50' : ''}`}
            style={{ backgroundColor: colors.grey5 }}>
            {isUploading ? (
              <ActivityIndicator
                size="small"
                color={isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
            ) : (
              <Ionicons
                name="camera"
                size={22}
                color={isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
            )}
            <Text className={`font-medium ${isDark ? 'text-white' : 'text-black'}`}>
              {isUploading ? 'Uploading...' : 'Take Photo'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={chooseFromLibrary}
            disabled={isUploading}
            className={`h-14 flex-row items-center rounded-lg px-4 py-3 ${isUploading ? 'opacity-50' : ''}`}
            style={{ backgroundColor: colors.grey5 }}>
            {isUploading ? (
              <ActivityIndicator
                size="small"
                color={isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
            ) : (
              <MaterialCommunityIcons
                name="image"
                size={22}
                color={isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
            )}
            <Text className={`font-medium ${isDark ? 'text-white' : 'text-black'}`}>
              {isUploading ? 'Uploading...' : 'Choose from Library'}
            </Text>
          </TouchableOpacity>
        </View>
      </BottomSheetView>
    </BottomSheet>
  );
});

ProfilePhotoUploadBottomSheet.displayName = 'ProfilePhotoUploadBottomSheet';

export default ProfilePhotoUploadBottomSheet;
