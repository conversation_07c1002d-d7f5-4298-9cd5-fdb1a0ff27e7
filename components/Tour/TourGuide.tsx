import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { CopilotProvider, CopilotStep, walkthroughable, useCopilot } from 'react-native-copilot';
import { useColorScheme } from '~/lib/useColorScheme';

// Create walkthroughable components
const WalkthroughableView = walkthroughable(View);

// Custom tooltip component
const TooltipComponent = () => {
  const { isFirstStep, isLastStep, goToNext, goToPrev, stop, currentStep } = useCopilot();
  const { isDarkColorScheme } = useColorScheme();

  return (
    <View
      className={`rounded-xl p-4 shadow-lg ${
        isDarkColorScheme ? 'bg-dark-card' : 'bg-light-card'
      } max-w-[300px]`}>
      <Text
        className={`mb-2 font-medium text-base ${
          isDarkColorScheme ? 'text-white' : 'text-gray-800'
        }`}>
        {currentStep?.text}
      </Text>

      <View className="flex-row justify-between mt-4">
        {!isFirstStep && (
          <Text
            className={`font-medium text-sm ${
              isDarkColorScheme ? 'text-gray-300' : 'text-gray-600'
            }`}
            onPress={goToPrev}>
            Previous
          </Text>
        )}
        <View style={{ flex: 1 }} />
        {!isLastStep ? (
          <Text
            className="text-sm font-medium text-light-primary dark:text-dark-primary"
            onPress={goToNext}>
            Next
          </Text>
        ) : (
          <Text
            className="text-sm font-medium text-light-primary dark:text-dark-primary"
            onPress={stop}>
            Finish
          </Text>
        )}
      </View>
    </View>
  );
};

// Tour guide component
const TourGuideWrapper = ({ children, showTour, onFinish }: any) => {
  return (
    <CopilotProvider
      tooltipComponent={TooltipComponent}
      stepNumberComponent={() => null}
      arrowColor="#8b5cf6"
      backdropColor="rgba(0, 0, 0, 0.7)"
      overlay="svg"
      verticalOffset={36}>
      {children}
    </CopilotProvider>
  );
};

// Hook to manage tour state
export const useTourGuide = () => {
  const [showTour, setShowTour] = useState(false);
  const { start, copilotEvents } = useCopilot();

  useEffect(() => {
    // Check if the tour has been shown before
    const checkTourStatus = async () => {
      try {
        setShowTour(true);
      } catch (error) {
        console.error('Error checking tour status:', error);
      }
    };

    checkTourStatus();

    // Set up event listener for when tour ends
    const handleTourEnd = async () => {
      try {
        await AsyncStorage.setItem('tourCompleted', 'true');
        setShowTour(false);
      } catch (error) {
        console.error('Error saving tour status:', error);
      }
    };

    copilotEvents.on('stop', handleTourEnd);

    // Start the tour if it should be shown
    if (showTour) {
      // Small delay to ensure components are mounted
      setTimeout(() => {
        start();
      }, 1000);
    }

    return () => {
      copilotEvents.off('stop', handleTourEnd);
    };
  }, [copilotEvents, start, showTour]);

  // Function to manually reset the tour (for testing)
  const resetTour = async () => {
    try {
      setShowTour(true);
      setTimeout(() => {
        start();
      }, 500);
    } catch (error) {
      console.error('Error resetting tour:', error);
    }
  };

  return {
    showTour,
    setShowTour,
    resetTour,
  };
};

export { TourGuideWrapper, WalkthroughableView };
