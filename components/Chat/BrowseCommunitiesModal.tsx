import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
  View,
  TouchableOpacity,
  FlatList,
  Image,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BottomSheetView, BottomSheetBackdrop, BottomSheetModal } from '@gorhom/bottom-sheet';
import { Text } from '~/components/nativewindui/Text';
import { Box } from '~/components/ui/box';
import { Input, InputField, InputSlot } from '~/components/ui/input';
import { CommunityService } from '../../services/CommunityService';
import { UserStore } from '../../store/store';

interface Community {
  id: number;
  name: string;
  description: string;
  avatar: string;
  memberCount: number;
}

interface BrowseCommunitiesModalProps {
  visible: boolean;
  onClose: () => void;
  onJoinCommunity: (community: Community) => void;
  colors: any;
}

const BrowseCommunitiesModal = ({
  visible,
  onClose,
  onJoinCommunity,
  colors,
}: BrowseCommunitiesModalProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [communities, setCommunities] = useState<Community[]>([]);
  const [userCommunities, setUserCommunities] = useState<string[]>([]);

  // Ref for bottom sheet modal
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);

  // Define snap points
  const snapPoints = useMemo(() => ['95%'], []);

  const userData = UserStore((state: any) => state.user);

  // Mock data for demonstration
  const mockCommunities: Community[] = [
    {
      id: 101,
      name: 'Photography Enthusiasts',
      description: 'A group for sharing photography tips and showcasing your best shots.',
      avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
      memberCount: 234,
    },
    {
      id: 102,
      name: 'Book Club',
      description: 'Discover new books and discuss your favorite reads with fellow book lovers.',
      avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
      memberCount: 156,
    },
    {
      id: 103,
      name: 'Fitness & Health',
      description: 'Share workout routines, healthy recipes, and support each other',
      avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
      memberCount: 412,
    },
    {
      id: 104,
      name: 'Travel Adventures',
      description: 'Exchange travel tips, destination ideas, and your amazing travel stories.',
      avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
      memberCount: 278,
    },
    {
      id: 105,
      name: 'Tech Talk',
      description: 'Discuss the latest in technology, gadgets, apps, and digital trends.',
      avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
      memberCount: 341,
    },
  ];

  useEffect(() => {
    if (visible) {
      bottomSheetModalRef.current?.present();
      loadCommunities();
    } else {
      bottomSheetModalRef.current?.dismiss();
    }
  }, [visible, loadCommunities]);

  const loadCommunities = useCallback(async () => {
    if (!userData?.id) return;

    try {
      setLoading(true);

      // Load all communities
      const communitiesResponse = await CommunityService.getCommunities();

      // Load user's communities to filter them out
      const userCommunitiesResponse = await CommunityService.getUserCommunities(userData.id);

      if (communitiesResponse.success && userCommunitiesResponse.success) {
        const allCommunities = communitiesResponse.communities || [];
        const joinedCommunityIds =
          userCommunitiesResponse.communities?.map((c: any) => c.id.toString()) || [];

        // Filter out communities the user has already joined
        const availableCommunities = allCommunities.filter(
          (community: any) => !joinedCommunityIds.includes(community.id.toString())
        );

        setCommunities(availableCommunities);
        setUserCommunities(joinedCommunityIds);
      }
    } catch (error) {
      console.error('Error loading communities:', error);
      // Fallback to mock data if API fails
      setCommunities(mockCommunities);
    } finally {
      setLoading(false);
    }
  }, [userData?.id]);

  const handleSheetChanges = useCallback(
    (index: number) => {
      if (index === -1) {
        onClose();
      }
    },
    [onClose]
  );

  const filteredCommunities = communities.filter(
    (community) =>
      community.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      community.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderCommunityItem = ({ item }: { item: Community }) => (
    <View className="flex-row border-b p-4" style={{ borderBottomColor: colors.grey5 }}>
      <Image source={{ uri: item.avatar }} className="h-[60px] w-[60px] rounded-full" />
      <View className="ml-3 flex-1 justify-center">
        <Text className="mb-1 text-base font-semibold" style={{ color: colors.foreground }}>
          {item.name}
        </Text>
        <Text className="mb-1.5 text-sm" style={{ color: colors.grey }} numberOfLines={2}>
          {item.description}
        </Text>
        <Text className="text-xs" style={{ color: colors.grey }}>
          {item.memberCount} {item.memberCount === 1 ? 'member' : 'members'}
        </Text>
      </View>
      <TouchableOpacity
        className="ml-2 self-center rounded-full px-4 py-2"
        style={{ backgroundColor: colors.primary }}
        onPress={() => onJoinCommunity(item)}>
        <Text className="font-semibold text-white">Join</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <BottomSheetModal
      ref={bottomSheetModalRef}
      index={0}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      backdropComponent={(props) => (
        <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} opacity={0.6} />
      )}
      backgroundStyle={{ backgroundColor: colors.background }}
      handleIndicatorStyle={{ backgroundColor: colors.grey }}>
      <BottomSheetView className="flex-1" style={{ backgroundColor: colors.background }}>
        <View
          className="flex-row items-center justify-between border-b px-4 py-4"
          style={{ borderBottomColor: colors.grey5 }}>
          {/*    <TouchableOpacity className="items-center justify-center w-10 h-10" onPress={onClose}>
            <Ionicons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity> */}
          <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
            Browse Communities
          </Text>
          <View className="w-10" />
        </View>

        <Box className="p-4">
          <View
            className="flex-row items-center rounded-lg px-3"
            style={{ backgroundColor: colors.card }}>
            <Ionicons name="search" size={20} color={colors.grey} className="mr-2" />
            <TextInput
              className="h-[46px] flex-1 text-base"
              placeholder="Search communities"
              placeholderTextColor={colors.grey}
              value={searchQuery}
              onChangeText={setSearchQuery}
              style={{ color: colors.foreground }}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => setSearchQuery('')} className="p-1">
                <Ionicons name="close-circle" size={20} color={colors.grey} />
              </TouchableOpacity>
            ) : null}
          </View>
        </Box>

        {loading ? (
          <View className="flex-1 items-center justify-center">
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : (
          <FlatList
            data={filteredCommunities}
            keyExtractor={(item) => item.id.toString()}
            renderItem={renderCommunityItem}
            className="flex-1"
            contentContainerStyle={{ paddingBottom: 20 }}
            ListEmptyComponent={
              <View className="flex-1 items-center justify-center pt-[100px]">
                <Ionicons name="search-outline" size={64} color={colors.grey} />
                <Text className="mt-4 text-base" style={{ color: colors.foreground }}>
                  No communities found
                </Text>
              </View>
            }
          />
        )}
      </BottomSheetView>
    </BottomSheetModal>
  );
};

export default BrowseCommunitiesModal;
