import React from 'react';
import { View, Text, TouchableOpacity, FlatList, Image, Modal, SafeAreaView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { GroupMember } from '../../types/chat_type';
import { useColorScheme } from '../../lib/useColorScheme';

interface GroupMembersModalProps {
  isVisible: boolean;
  onClose: () => void;
  members: GroupMember[];
  groupName: string;
  currentUserId: string;
  onRemoveMember?: (memberId: string) => void;
  canManageMembers?: boolean;
}

const GroupMembersModal = ({
  isVisible,
  onClose,
  members,
  groupName,
  currentUserId,
  onRemoveMember,
  canManageMembers = false,
}: GroupMembersModalProps) => {
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const renderMember = ({ item }: { item: GroupMember }) => {
    const isCurrentUser = item.id === currentUserId;
    const canRemove = canManageMembers && !isCurrentUser && !item.isAdmin;

    return (
      <View
        className="flex-row items-center px-4 py-3 border-b"
        style={{ borderBottomColor: colors.grey5 }}>
        <Image
          source={{
            uri:
              (item.avatar.length > 0 ? item.avatar[item.avatar.length - 1]?.secureUrl : null) ||
              'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
          }}
          className="w-12 h-12 rounded-full"
        />
        <View className="flex-1 ml-3">
          <View className="flex-row items-center">
            <Text
              className="text-base font-medium"
              style={{ color: colors.foreground }}
              numberOfLines={1}>
              {item.name}
              {isCurrentUser && ' (You)'}
            </Text>
            {item.isAdmin && (
              <View
                className="px-2 py-1 ml-2 rounded-full"
                style={{ backgroundColor: colors.primary }}>
                <Text className="text-xs font-medium text-white">Admin</Text>
              </View>
            )}
          </View>
          <Text className="mt-1 text-sm" style={{ color: colors.grey }} numberOfLines={1}>
            {item.email}
          </Text>
        </View>
        {canRemove && (
          <TouchableOpacity onPress={() => onRemoveMember?.(item.id)} className="p-2">
            <Ionicons name="remove-circle-outline" size={20} color={colors.error} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderHeader = () => (
    <View className="px-4 py-3">
      <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
        {groupName}
      </Text>
      <Text className="mt-1 text-sm" style={{ color: colors.grey }}>
        {members.length} {members.length === 1 ? 'member' : 'members'}
      </Text>
    </View>
  );

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}>
      <SafeAreaView className="flex-1" style={{ backgroundColor: colors.background }}>
        {/* Header */}
        <View
          className="flex-row items-center justify-between px-4 py-3 border-b"
          style={{ borderBottomColor: colors.grey5 }}>
          <Text className="text-xl font-semibold" style={{ color: colors.foreground }}>
            Members
          </Text>
          <TouchableOpacity onPress={onClose} className="p-1">
            <Ionicons name="close" size={24} color={colors.foreground} />
          </TouchableOpacity>
        </View>

        {/* Members List */}
        <FlatList
          data={members}
          keyExtractor={(item) => item.id}
          renderItem={renderMember}
          ListHeaderComponent={renderHeader}
          showsVerticalScrollIndicator={false}
          className="flex-1"
          ListEmptyComponent={() => (
            <View className="items-center justify-center flex-1 py-12">
              <Ionicons name="people-outline" size={80} color={colors.grey} />
              <Text
                className="mt-4 text-lg font-medium text-center"
                style={{ color: colors.foreground }}>
                No members found
              </Text>
            </View>
          )}
        />
      </SafeAreaView>
    </Modal>
  );
};

export default GroupMembersModal;
