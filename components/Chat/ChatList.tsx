import React from 'react';
import { View, FlatList, TouchableOpacity, Image, Text, ActivityIndicator } from 'react-native';

import { Ionicons } from '@expo/vector-icons';

import { ChatItem } from '../../types/chat_type';

interface ChatListProps {
  data: ChatItem[];
  type: 'direct' | 'community' | 'event';
  onSelect: (item: ChatItem, type: 'direct' | 'community' | 'event') => void;
  isDark: boolean;
  colors: any;
  headerTitle: string;
  showAddButton?: boolean;
  onAddPress?: () => void;
  onBrowsePress?: () => void;
  isLoading?: boolean;
}

const ChatList = ({
  data,
  type,
  onSelect,
  isDark,
  colors,
  headerTitle,
  showAddButton = false,
  onAddPress,
  onBrowsePress,
  isLoading = false,
}: ChatListProps) => {
  const renderItem = ({ item }: { item: ChatItem }) => {
    return (
      <TouchableOpacity
        className="flex-row items-center py-3 border-b"
        style={{ borderBottomColor: colors.grey5 }}
        onPress={() => onSelect(item, type)}
        activeOpacity={0.7}>
        <Image
          source={{
            uri: Array.isArray(item.avatar)
              ? item.avatar[item.avatar.length - 1]?.secureUrl || item.avatar[0]
              : item.avatar,
          }}
          className="rounded-full w-15 h-15"
          style={{ width: 60, height: 60 }}
        />
        <View className="justify-center flex-1 ml-4">
          <View className="flex-row items-center justify-between mb-1">
            <Text
              className="flex-1 mr-2 text-lg font-medium"
              style={{ color: colors.foreground }}
              numberOfLines={1}
              ellipsizeMode="tail">
              {item.name}
            </Text>
            <Text className="text-xs" style={{ color: colors.grey }}>
              {item.timestamp}
            </Text>
          </View>
          <View className="flex-row items-center justify-between">
            <Text
              className="flex-1 mr-2 text-sm"
              style={{ color: colors.grey }}
              numberOfLines={1}
              ellipsizeMode="tail">
              {item.lastMessage || 'No messages yet'}
            </Text>
            {item.unread && item.unread > 0 && (
              <View
                className="items-center justify-center w-5 h-5 rounded-full"
                style={{ backgroundColor: colors.primary }}>
                <Text className="text-xs font-semibold text-white">{item.unread}</Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => {
    if (type === 'direct') {
      return (
        <View className="items-center justify-center flex-1 py-12">
          <Ionicons name="chatbubble-outline" size={80} color={colors.grey} />
          <Text
            className="mt-4 mb-2 text-lg font-medium text-center"
            style={{ color: colors.foreground }}>
            No chats available
          </Text>
          <View className="flex-row items-center justify-center">
            <Text className="text-base text-center" style={{ color: colors.grey }}>
              Add friend, click the
            </Text>
            <Ionicons
              name="add-circle-outline"
              size={20}
              color={colors.primary}
              style={{ marginHorizontal: 4 }}
            />
            <Text className="text-base text-center" style={{ color: colors.grey }}>
              icon to start
            </Text>
          </View>
        </View>
      );
    }

    if (type === 'event') {
      return (
        <View className="items-center justify-center flex-1 py-12">
          <Ionicons name="calendar-outline" size={80} color={colors.grey} />
          <Text
            className="mt-4 mb-2 text-lg font-medium text-center"
            style={{ color: colors.foreground }}>
            No event chats available
          </Text>
          <Text className="text-base text-center" style={{ color: colors.grey }}>
            Chats only appear after joining an event
          </Text>
        </View>
      );
    }

    if (type === 'community') {
      return (
        <View className="items-center justify-center flex-1 py-12">
          <Ionicons name="people-outline" size={80} color={colors.grey} />
          <Text
            className="mt-4 mb-2 text-lg font-medium text-center"
            style={{ color: colors.foreground }}>
            You haven't joined a community
          </Text>
          <View className="flex-row flex-wrap items-center justify-center">
            <Text className="text-base text-center" style={{ color: colors.grey }}>
              Click
            </Text>
            <Ionicons
              name="search"
              size={20}
              color={colors.primary}
              style={{ marginHorizontal: 4 }}
            />
            <Text className="text-base text-center" style={{ color: colors.grey }}>
              to join or click
            </Text>
            <Ionicons name="add" size={20} color={colors.primary} style={{ marginHorizontal: 4 }} />
            <Text className="text-base text-center" style={{ color: colors.grey }}>
              to create one
            </Text>
          </View>
        </View>
      );
    }

    return null;
  };

  const renderLoadingState = () => {
    return (
      <View className="items-center justify-center flex-1 py-8">
        <ActivityIndicator size="large" color={colors.primary} />
        <Text className="mt-4 text-base" style={{ color: colors.grey }}>
          Loading chats...
        </Text>
      </View>
    );
  };

  const isCommunity = type === 'community';

  return (
    <View className="flex-1 px-4">
      <View className="flex-row items-center justify-between my-3">
        <Text className="text-xl font-bold" style={{ color: colors.foreground }}>
          {headerTitle}
        </Text>
        <View className="flex-row">
          {isCommunity && (
            <TouchableOpacity className="p-1 mr-2" onPress={onBrowsePress}>
              <Ionicons name="search" size={24} color={colors.primary} />
            </TouchableOpacity>
          )}
          {(showAddButton || isCommunity) && (
            <TouchableOpacity className="p-1" onPress={onAddPress}>
              <Ionicons
                name={isCommunity ? 'add' : 'add-circle-outline'}
                size={24}
                color={colors.primary}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>
      {isLoading ? (
        renderLoadingState()
      ) : (
        <FlatList
          data={data}
          keyExtractor={(item) => item.id.toString()}
          renderItem={renderItem}
          className="flex-1"
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState}
        />
      )}
    </View>
  );
};

export default ChatList;
