import { Ionicons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { useFocusEffect } from 'expo-router';
import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
  ActivityIndicator,
  BackHandler,
} from 'react-native';
import { Toast } from 'toastify-react-native';

import { FriendService } from '~/services/FriendService';
import { UserStore } from '~/store/store';
import { ChatItem, Friend } from '~/types/chat_type';

interface NewChatSheetProps {
  bottomSheetRef: React.RefObject<BottomSheet | null>;
  onSelectFriend: (friend: Friend) => void;
  colors: any;
  isDark: boolean;
  friendsWithoutChats?: Friend[]; // Add this prop
  isOpen?: boolean; // Add this prop to track if sheet is open
  onClose?: () => void; // Add this prop to close the sheet
  existingChats?: ChatItem[]; // Add this prop for existing chats
  onNavigateToChat?: (chat: ChatItem) => void; // Add this prop to navigate to existing chat
}

const NewChatSheet = ({
  bottomSheetRef,
  onSelectFriend,
  colors,
  isDark,
  friendsWithoutChats = [],
  isOpen = false,
  onClose,
  existingChats = [],
  onNavigateToChat,
}: NewChatSheetProps) => {
  const user = UserStore((state: any) => state.user);
  const [searchQuery, setSearchQuery] = useState('');
  const [friends, setFriends] = useState<Friend[]>([]);
  const [filteredFriends, setFilteredFriends] = useState<Friend[]>([]);
  const [loading, setLoading] = useState(true);
  const [creatingChat, setCreatingChat] = useState(false); // New state for chat creation

  // Fetch friends on component mount
  useFocusEffect(
    useCallback(() => {
      fetchFriends();
    }, [user?.id])
  );

  // Handle back button to close the sheet when it's open
  useEffect(() => {
    const handleBackPress = () => {
      if (isOpen) {
        // Close the new chat sheet
        if (onClose) {
          onClose();
        } else {
          bottomSheetRef.current?.close();
        }
        return true; // Prevent default back behavior
      }
      return false; // Let default back behavior happen
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);

    return () => {
      backHandler.remove();
    };
  }, [isOpen, onClose, bottomSheetRef]);

  const fetchFriends = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const response = await FriendService.getFriends(user.id);
      if (response.success && response.body) {
        // Transform the response to match our Friend interface
        const friendsData = response.body.friends.map((friend: any) => ({
          id: friend.id,
          name: friend.fullName || friend.name,
          avatar:
            friend.profilePicture?.[0]?.secureUrl ||
            friend.profilePhoto ||
            'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
          status: friend.isOnline ? 'online' : 'offline',
        }));
        setFriends(friendsData);
        setFilteredFriends(friendsData);
      } else {
        setFriends([]);
        setFilteredFriends([]);
      }
    } catch (error) {
      console.error('Error fetching friends:', error);
      setFriends([]);
      setFilteredFriends([]);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load friends',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text.trim() === '') {
      setFilteredFriends(friends);
    } else {
      const filtered = friends.filter((friend) =>
        friend.name.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredFriends(filtered);
    }
  };

  const handleSelectFriend = async (friend: Friend) => {
    // Check if there is an existing chat with the selected friend
    const existingChat = existingChats.find((chat) => chat.id === friend.id);

    if (existingChat && onNavigateToChat) {
      // If an existing chat is found, navigate to that chat
      onNavigateToChat(existingChat);
    } else {
      // Set loading state for creating chat
      setCreatingChat(true);

      try {
        // Otherwise, create a new chat (existing logic)
        onSelectFriend(friend);
      } finally {
        setCreatingChat(false);
      }
    }
  };

  const renderFriend = ({ item }: { item: Friend }) => (
    <TouchableOpacity
      className="flex-row items-center px-4 py-3 border-b"
      style={{ borderBottomColor: colors.grey5 }}
      onPress={() => handleSelectFriend(item)}
      disabled={creatingChat}>
      <View className="relative">
        <Image source={{ uri: item.avatar }} className="w-12 h-12 rounded-full" />
        <View
          className="absolute bottom-0 right-0 w-3 h-3 border-2 rounded-full"
          style={{
            borderColor: isDark ? colors.background : colors.root,
          }}
        />
      </View>
      <View className="flex-1 ml-3">
        <Text className="text-base font-medium" style={{ color: colors.foreground }}>
          {item.name}
        </Text>
      </View>
      {creatingChat && <ActivityIndicator size="small" color={colors.primary} />}
    </TouchableOpacity>
  );

  return (
    <BottomSheetView style={{ backgroundColor: colors.background, flex: 1 }}>
      <View className="px-4 pt-2 pb-6">
        <Text className="mb-4 text-xl font-bold text-center" style={{ color: colors.foreground }}>
          New Chat
        </Text>
        {creatingChat && (
          <View className="flex-row items-center justify-center flex-1 w-full ">
            <ActivityIndicator size="large" color={colors.primary} />
            <Text className="text-base text-center " style={{ color: colors.grey }}>
              Loading chat...
            </Text>
          </View>
        )}

        <View className="mb-4">
          <View
            className="flex-row items-center rounded-xl px-3 py-2.5"
            style={{ backgroundColor: colors.grey5 }}>
            <Ionicons name="search" size={20} color={colors.grey} />
            <TextInput
              className="flex-1 ml-2 text-base"
              placeholder="Search friends..."
              placeholderTextColor={colors.grey}
              value={searchQuery}
              onChangeText={handleSearch}
              style={{ color: colors.foreground }}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => handleSearch('')}>
                <Ionicons name="close-circle" size={20} color={colors.grey} />
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        {loading ? (
          <View className="items-center justify-center flex-1 py-10">
            <ActivityIndicator size="large" color={colors.primary} />
            <Text className="mt-4 text-base text-center" style={{ color: colors.grey }}>
              Loading friends...
            </Text>
          </View>
        ) : friends.length === 0 ? (
          <View className="items-center justify-center flex-1 px-6 py-10">
            <View className="items-center mt-48 h-52">
              <View
                className="items-center justify-center w-20 h-20 mb-4 rounded-full"
                style={{ backgroundColor: colors.grey5 }}>
                <Ionicons name="people-outline" size={36} color={colors.grey} />
              </View>
              <Text
                className="mb-2 text-lg font-bold text-center"
                style={{ color: colors.foreground }}>
                You don't have any friends yet
              </Text>
              <Text className="mb-6 text-sm leading-5 text-center" style={{ color: colors.grey }}>
                Start building your network by adding friends! Once you add friends, they'll appear
                here and you can start chatting with them.
              </Text>
            </View>
          </View>
        ) : filteredFriends.length > 0 ? (
          <FlatList
            data={filteredFriends}
            keyExtractor={(item) => item.id}
            renderItem={renderFriend}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 40 }}
          />
        ) : (
          <View className="items-center justify-center flex-1 py-10">
            <Ionicons name="search" size={48} color={colors.grey} />
            <Text className="mt-4 text-base text-center" style={{ color: colors.grey }}>
              No friends found with "{searchQuery}"
            </Text>
          </View>
        )}
      </View>
    </BottomSheetView>
  );
};

export default NewChatSheet;
