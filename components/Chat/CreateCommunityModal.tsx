import React, { useState, useRef, useMemo, useCallback, useEffect } from 'react';
import {
  View,
  Text as RNText,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  Image,
  ScrollView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import ImagePicker from 'react-native-image-crop-picker';
import { BottomSheetView, BottomSheetBackdrop, BottomSheetModal } from '@gorhom/bottom-sheet';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  FormControl,
  FormControlError,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
} from '@/components/ui/form-control';
import { Text } from '~/components/nativewindui/Text';
import { Button, ButtonText } from '@/components/ui/button';
import { Input, InputField } from '~/components/ui/input';
import { Box } from '~/components/ui/box';
import { VStack } from '~/components/ui/vstack';

// Define validation schema with zod
const communitySchema = z.object({
  name: z.string().min(1, 'Community name is required'),
  description: z.string().min(1, 'Description is required'),
});

type CommunityFormData = z.infer<typeof communitySchema>;

interface CreateCommunityModalProps {
  visible: boolean;
  onClose: () => void;
  onCreateCommunity: (name: string, description: string, imageUri: string) => void;
  colors: any;
  isDark?: boolean;
}

const CreateCommunityModal = ({
  visible,
  onClose,
  onCreateCommunity,
  colors,
  isDark,
}: CreateCommunityModalProps) => {
  const [imageUri, setImageUri] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Ref for bottom sheet modal
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);

  // Define snap points
  const snapPoints = useMemo(() => ['100%'], []);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CommunityFormData>({
    resolver: zodResolver(communitySchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  useEffect(() => {
    if (visible) {
      bottomSheetModalRef.current?.present();
    } else {
      bottomSheetModalRef.current?.dismiss();
    }
  }, [visible]);

  const handleSheetChanges = useCallback(
    (index: number) => {
      if (index === -1) {
        resetForm();
        onClose();
      }
    },
    [onClose]
  );

  const pickImage = async () => {
    try {
      const image = await ImagePicker.openPicker({
        width: 400,
        height: 400,
        cropping: true,
        compressImageQuality: 0.8,
        includeBase64: false,
        freeStyleCropEnabled: true,
        mediaType: 'photo',
        cropperStatusBarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarWidgetColor: Platform.OS === 'android' ? colors.foreground : undefined,
        cropperActiveWidgetColor: Platform.OS === 'android' ? colors.primary : undefined,
        showCropGuidelines: Platform.OS === 'android' ? true : undefined,
        showCropFrame: Platform.OS === 'android' ? true : undefined,
        hideBottomControls: Platform.OS === 'android' ? false : undefined,
        enableRotationGesture: Platform.OS === 'android' ? true : undefined,
        disableCropperColorSetters: Platform.OS === 'android' ? false : undefined,
        // iOS-specific configurations
        cropperChooseColor: Platform.OS === 'ios' ? colors.primary : undefined,
        cropperCancelColor: Platform.OS === 'ios' ? colors.foreground : undefined,
        avoidEmptySpaceAroundImage: Platform.OS === 'ios' ? true : undefined,
        cropperToolbarTitle: 'Edit Photo',
      });

      setImageUri(image.path);
    } catch (error: any) {
      if (error.code !== 'E_PICKER_CANCELLED') {
        console.log('Error picking an image:', error);
      }
    }
  };

  const onSubmit = (data: CommunityFormData) => {
    if (!imageUri) {
      // Show error about image
      return;
    }

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      onCreateCommunity(data.name, data.description, imageUri);
      resetForm();
      bottomSheetModalRef.current?.dismiss();
      setIsLoading(false);
    }, 1000);
  };

  const resetForm = () => {
    reset();
    setImageUri('');
  };

  const handleClose = () => {
    resetForm();
    bottomSheetModalRef.current?.dismiss();
    onClose();
  };

  return (
    <BottomSheetModal
      ref={bottomSheetModalRef}
      index={0}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      backdropComponent={(props) => (
        <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} opacity={0.6} />
      )}
      backgroundStyle={{ backgroundColor: colors.background }}
      handleIndicatorStyle={{ backgroundColor: colors.grey }}>
      <BottomSheetView className="flex-1">
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View className="flex-1">
            <View
              className="flex-row items-center justify-between border-b px-5 py-3.5"
              style={{ borderBottomColor: colors.grey5 }}>
              <TouchableOpacity
                className="items-center justify-center w-10 h-10"
                onPress={handleClose}>
                <Ionicons name="close" size={24} color={colors.foreground} />
              </TouchableOpacity>
              <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
                Create New Community
              </Text>
              <View className="w-10" />
            </View>

            <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
              <Box className="p-5">
                <View>
                  <View className="flex-row items-center gap-4 ">
                    {imageUri ? (
                      <Image
                        source={{ uri: imageUri }}
                        className="h-[120px] w-[120px] rounded-full"
                      />
                    ) : (
                      <View
                        className=" h-[120px] w-[120px] items-center justify-center rounded-full"
                        style={{ backgroundColor: colors.card }}>
                        <Ionicons name="people" size={40} color={colors.grey} />
                      </View>
                    )}
                    <TouchableOpacity
                      className="rounded-full px-5 py-2.5"
                      style={{ backgroundColor: colors.primary }}
                      onPress={pickImage}>
                      <Text className="font-semibold text-white">
                        {imageUri ? 'Change Image' : 'Upload Image'}
                      </Text>
                    </TouchableOpacity>
                  </View>
                  {!imageUri && (
                    <RNText className="text-red-500 ">Please upload a community image</RNText>
                  )}
                </View>

                <VStack space="lg" className="mb-6">
                  <FormControl isInvalid={!!errors.name}>
                    <FormControlLabel>
                      <FormControlLabelText style={{ color: colors.foreground }}>
                        Community Name*
                      </FormControlLabelText>
                    </FormControlLabel>
                    <Controller
                      name="name"
                      control={control}
                      render={({ field: { onChange, onBlur, value } }) => (
                        <Input
                          variant="outline"
                          className="border-0 h-14 rounded-xl"
                          style={{
                            backgroundColor: colors.grey5,
                          }}>
                          <InputField
                            placeholder="Enter community name"
                            onBlur={onBlur}
                            onChangeText={onChange}
                            value={value}
                            className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                            placeholderTextColor={isDark ? colors.grey : colors.grey}
                          />
                        </Input>
                      )}
                    />
                    <FormControlError>
                      <FormControlErrorText className="text-red-500">
                        {errors.name?.message}
                      </FormControlErrorText>
                    </FormControlError>
                  </FormControl>

                  <FormControl isInvalid={!!errors.description}>
                    <FormControlLabel>
                      <FormControlLabelText style={{ color: colors.foreground }}>
                        Description*
                      </FormControlLabelText>
                    </FormControlLabel>
                    <Controller
                      name="description"
                      control={control}
                      render={({ field: { onChange, onBlur, value } }) => (
                        <Input
                          variant="outline"
                          className="border-0 min-h-14 rounded-xl"
                          style={{
                            backgroundColor: colors.grey5,
                          }}>
                          <InputField
                            placeholder="Describe your community"
                            onBlur={onBlur}
                            onChangeText={onChange}
                            value={value}
                            className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                            placeholderTextColor={isDark ? colors.grey : colors.grey}
                            multiline
                            numberOfLines={2}
                            returnKeyType="done"
                          />
                        </Input>
                      )}
                    />
                    <FormControlError>
                      <FormControlErrorText className="text-red-500">
                        {errors.description?.message}
                      </FormControlErrorText>
                    </FormControlError>
                  </FormControl>
                </VStack>

                <Button
                  className="mt-4 h-14 rounded-xl"
                  style={{ backgroundColor: colors.primary }}
                  onPress={handleSubmit(onSubmit)}
                  isDisabled={isLoading || !imageUri}>
                  <ButtonText className="font-medium text-white">
                    {isLoading ? 'Creating...' : 'Create Community'}
                  </ButtonText>
                </Button>
              </Box>
            </ScrollView>
          </View>
        </TouchableWithoutFeedback>
      </BottomSheetView>
    </BottomSheetModal>
  );
};

export default CreateCommunityModal;
