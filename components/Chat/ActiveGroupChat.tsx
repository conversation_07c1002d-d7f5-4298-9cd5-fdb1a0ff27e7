import React, { useEffect, useLayoutEffect, useState } from 'react';
import {
  View,
  TouchableOpacity,
  Image,
  Text,
  StyleSheet,
  Keyboard,
  KeyboardAvoidingView,
  ActivityIndicator,
  Platform,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  GiftedChat,
  Bubble,
  Send,
  InputToolbar,
  Actions,
  MessageImage,
  Day,
  Composer,
} from 'react-native-gifted-chat';
import { GroupChatItem, GroupMessage, GroupMember } from '../../types/chat_type';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';

import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation } from 'expo-router';
import PhotoViewModal from '../Profile/PhotoViewModal';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';

interface ActiveGroupChatProps {
  selectedGroupChat: GroupChatItem;
  messages: GroupMessage[];
  onSend: (messages: any) => void;
  onBack: () => void;
  renderActions?: any;
  colors: any;
  isDark: boolean;
  user: {
    _id: string;
    name: string;
    avatar: string;
  };
  renderSend?: any;
  renderInputToolbar?: any;
  renderComposer?: any;
  messagesLoading?: boolean;
  onRetryMessage?: (message: any) => void;
  groupMembers?: GroupMember[];
  onViewMembers?: () => void;
  onLeaveGroup?: () => void;
}

interface RenderProps {
  text?: string;
  onSend?: (message: any, shouldReset: boolean) => void;
  textInputProps?: any;
  [key: string]: any;
}

const ActiveGroupChat = ({
  selectedGroupChat,
  messages,
  onSend,
  onBack,
  renderActions,
  colors,
  isDark,
  user,
  renderSend: customRenderSend,
  renderInputToolbar: customRenderInputToolbar,
  renderComposer: customRenderComposer,
  messagesLoading = false,
  onRetryMessage,
  groupMembers = [],
  onViewMembers,
  onLeaveGroup,
}: ActiveGroupChatProps) => {
  const [onFocus, setOnFocus] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState<{ id: string; secureUrl: string } | null>(
    null
  );
  const insets = useSafeAreaInsets();
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const navigation = useNavigation();

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardVisible(false);
    });

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  const renderFooter = (props) => {
    return <View {...props} height={40}></View>;
  };

  const renderBubble = (props) => {
    const isCurrentUser = props.currentMessage.user._id === user._id;

    return (
      <View>
        <Bubble
          {...props}
          wrapperStyle={{
            left: {
              backgroundColor: colors.grey5,
              marginLeft: 8,
              marginRight: 50,
            },
            right: {
              backgroundColor: colors.primary,
              marginLeft: 50,
              marginRight: 8,
            },
          }}
          textStyle={{
            left: {
              color: colors.foreground,
              fontFamily: 'Regular',
            },
            right: {
              color: 'white',
              fontFamily: 'Regular',
            },
          }}
          timeTextStyle={{
            left: {
              color: colors.grey,
              fontFamily: 'Regular',
            },
            right: {
              color: 'rgba(255,255,255,0.8)',
              fontFamily: 'Regular',
            },
          }}
          usernameStyle={{
            color: colors.primary,
            fontFamily: 'Medium',
            fontSize: 12,
          }}
          renderUsernameOnMessage={!isCurrentUser}
        />
        {props.currentMessage.pending && (
          <View className="absolute bottom-1 right-3">
            <ActivityIndicator size="small" color={colors.grey} />
          </View>
        )}
        {props.currentMessage.failed && (
          <TouchableOpacity
            className="absolute bottom-1 right-3"
            onPress={() => onRetryMessage?.(props.currentMessage)}>
            <Ionicons name="refresh" size={16} color={colors.error} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderSend = (props: RenderProps) => {
    return (
      <Send {...props}>
        <View
          className="items-center justify-center w-10 h-10 mr-2 rounded-full"
          style={{ backgroundColor: colors.primary }}>
          <Ionicons name="send" size={18} color="white" />
        </View>
      </Send>
    );
  };

  const renderDay = (props) => {
    return (
      <Day
        {...props}
        textStyle={{
          color: colors.grey,
          fontFamily: 'Regular',
          fontSize: 12,
        }}
      />
    );
  };

  const renderMessageImage = (props: RenderProps) => {
    const { currentMessage } = props;
    if (currentMessage && currentMessage.image) {
      return (
        <TouchableOpacity
          onPress={() => {
            setSelectedImage({
              id: currentMessage._id || 'image',
              secureUrl: currentMessage.uploadUrl || currentMessage.image,
            });
            setIsModalVisible(true);
          }}>
          <Image
            source={{ uri: currentMessage.image }}
            style={{
              width: 150,
              height: 100,
              borderRadius: 13,
              margin: 3,
            }}
          />
        </TouchableOpacity>
      );
    }
    return null;
  };

  const handleGroupInfo = () => {
    Alert.alert(
      selectedGroupChat.name,
      `${selectedGroupChat.groupType} • ${groupMembers.length} members`,
      [
        {
          text: 'View Members',
          onPress: onViewMembers,
        },
        {
          text: 'Leave Group',
          style: 'destructive',
          onPress: () => {
            Alert.alert('Leave Group', 'Are you sure you want to leave this group?', [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Leave', style: 'destructive', onPress: onLeaveGroup },
            ]);
          },
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setOnFocus(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setOnFocus(false);
    });

    return () => {
      keyboardDidHideListener?.remove();
      keyboardDidShowListener?.remove();
    };
  }, []);

  useLayoutEffect(() => {
    navigation.setOptions({
      tabBarStyle: { display: 'none' },
    });

    return () => {
      navigation.setOptions({
        tabBarStyle: { display: 'flex' },
      });
    };
  }, [navigation]);

  const renderComposer = (props: RenderProps) => {
    return (
      <View
        className="flex-row items-center pt-2 border-t border-gray-400 "
        style={{
          backgroundColor: colors.background,
          borderColor: colors.grey5,
          paddingBottom:
            Platform.OS === 'ios' ? 0 : isKeyboardVisible ? verticalScale(30) : verticalScale(50),
        }}>
        {renderActions()}
        <Composer
          {...props}
          textInputProps={{
            onFocus: () => setOnFocus(true),
            onBlur: () => setOnFocus(false),
            blurOnSubmit: true,
            paddingVertical: 0,
            paddingHorizontal: 15,
            backgroundColor: colors.grey5,
            borderRadius: 30,
            borderColor: colors.grey5,
            borderWidth: 1,
            width: '80%',
            minHeight: 44,
            marginTop: 3,
            marginRight: 10,
            color: colors.foreground,
          }}></Composer>
        <Send {...props}>
          <View
            style={{
              justifyContent: 'center',
              height: '100%',
              marginRight: moderateScale(10),
              marginBottom: moderateScale(5),
              backgroundColor: '#4a4de7',
              width: 36,
              height: 36,
              borderRadius: 18,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Ionicons name="send-sharp" size={18} color={'white'} />
          </View>
        </Send>
      </View>
    );
  };

  return (
    <View className="flex-1" style={{ backgroundColor: colors.background }}>
      {/* Header */}
      <View
        className="flex-row items-center px-4 py-3 border-b"
        style={{
          borderBottomColor: colors.grey5,
          paddingBottom: isKeyboardVisible ? moderateScale(0) : moderateScale(0),
        }}>
        <TouchableOpacity onPress={onBack} className="mr-3">
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <TouchableOpacity onPress={handleGroupInfo} className="flex-row items-center flex-1">
          <Image source={{ uri: selectedGroupChat.avatar }} className="w-8 h-8 rounded-full" />
          <View className="ml-2.5 flex-1">
            <Text
              className="text-lg font-semibold"
              style={{ color: colors.foreground }}
              numberOfLines={1}>
              {selectedGroupChat.name}
            </Text>
            <Text className="text-xs" style={{ color: colors.grey }} numberOfLines={1}>
              {selectedGroupChat.groupType} • {groupMembers.length} members
            </Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity onPress={handleGroupInfo} className="p-2">
          <Ionicons name="information-circle-outline" size={24} color={colors.foreground} />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        className="flex-1"
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}>
        <GiftedChat
          messages={messages}
          onSend={(messages) => onSend(messages)}
          user={user}
          minInputToolbarHeight={0}
          bottomOffset={-27}
          renderBubble={renderBubble}
          renderInputToolbar={(props) => (
            <InputToolbar {...props} containerStyle={{ borderTopWidth: 0 }} />
          )}
          renderSend={customRenderSend || renderSend}
          renderComposer={renderComposer}
          renderDay={renderDay}
          renderMessageImage={renderMessageImage}
          renderFooter={messagesLoading ? renderFooter : undefined}
          showAvatarForEveryMessage={false}
          renderAvatarOnTop={false}
          maxComposerHeight={45}
          renderAvatar={null}
          showUserAvatar={{ left: false, right: false }}
          alwaysShowSend={true}
          isKeyboardInternallyHandled={true}
          scrollToBottomComponent={() => (
            <View
              className="items-center justify-center w-8 h-8 rounded-full"
              style={{
                backgroundColor: colors.primary,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.3,
                shadowRadius: 3,
                elevation: 3,
              }}>
              <Ionicons name="chevron-down" size={16} color="white" />
            </View>
          )}
          timeTextStyle={{
            left: { color: colors.grey, fontSize: 10 },
            right: { color: 'rgba(255, 255, 255, 0.7)', fontSize: 10 },
          }}
          textInputProps={{
            placeholder: 'Message...',
            placeholderTextColor: colors.grey,
            style: {
              color: colors.foreground,
              backgroundColor: isDark ? colors.grey5 : colors.grey6,
              borderRadius: 18,
              paddingHorizontal: 15,
              paddingVertical: 10,
              fontSize: 16,
            },
          }}
        />
      </KeyboardAvoidingView>
      <PhotoViewModal
        isVisible={isModalVisible}
        onClose={() => {
          setIsModalVisible(false);
          setSelectedImage(null);
        }}
        photo={selectedImage}
      />
    </View>
  );
};

export default ActiveGroupChat;
