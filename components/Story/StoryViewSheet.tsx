import React, { forwardRef, useRef, useImperativeHandle, useEffect } from 'react';
import { View, Text, TouchableOpacity, BackHandler } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';

import { RenderBackdrop } from '~/components/RenderBackdrop';
import { useColorScheme } from '~/lib/useColorScheme';

export interface StoryViewSheetHandle {
  present: (userId: number, userName: string, hasStory: boolean) => void;
  dismiss: () => void;
}

interface StoryViewSheetProps {
  onViewProfile?: (userId: number) => void;
  onViewStory?: (userId: number) => void;
}

const StoryViewSheet = forwardRef<StoryViewSheetHandle, StoryViewSheetProps>(
  ({ onViewProfile, onViewStory }, ref) => {
    const bottomSheetRef = useRef<BottomSheet>(null);
    const { colors, colorScheme } = useColorScheme();
    const isDark = colorScheme === 'dark';
    const router = useRouter();

    const [userId, setUserId] = React.useState<number | null>(null);
    const [userName, setUserName] = React.useState<string>('');
    const [hasStory, setHasStory] = React.useState<boolean>(false);

    const snapPoints = ['25%'];

    useImperativeHandle(ref, () => ({
      present: (userId: number, userName: string, hasStory: boolean) => {
        setUserId(userId);
        setUserName(userName);
        setHasStory(hasStory);
        bottomSheetRef.current?.expand();
      },
      dismiss: () => {
        bottomSheetRef.current?.close();
      },
    }));

    // Handle back button to close the sheet when it's open
    useEffect(() => {
      const handleBackPress = () => {
        if (userId) {
          // Close the story view sheet
          bottomSheetRef.current?.close();
          setUserId(null);
          return true; // Prevent default back behavior
        }
        return false; // Let default back behavior happen
      };

      const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);

      return () => {
        backHandler.remove();
      };
    }, [userId]);

    const handleViewProfile = () => {
      bottomSheetRef.current?.close();
      if (userId) {
        if (onViewProfile) {
          onViewProfile(userId);
        } else {
          router.push({
            pathname: '/profile',
            params: { userId },
          });
        }
      }
    };

    const handleViewStory = () => {
      bottomSheetRef.current?.close();
      if (userId && hasStory) {
        if (onViewStory) {
          onViewStory(userId);
        } else {
          router.push({
            pathname: '/story/view',
            params: { userId },
          });
        }
      }
    };

    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        enablePanDownToClose
        backdropComponent={RenderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <BottomSheetView className="flex-1 px-4 pt-2">
          <Text
            className={`mb-6 text-center font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            {userName}
          </Text>

          <TouchableOpacity className="flex-row items-center py-4" onPress={handleViewProfile}>
            <View
              className="items-center justify-center w-10 h-10 mr-4 rounded-full"
              style={{ backgroundColor: colors.grey5 }}>
              <Ionicons name="person" size={22} color={colors.foreground} />
            </View>
            <Text className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
              View Profile
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className={`flex-row items-center py-4 ${!hasStory ? 'opacity-50' : ''}`}
            onPress={handleViewStory}
            disabled={!hasStory}>
            <View
              className="items-center justify-center w-10 h-10 mr-4 rounded-full"
              style={{ backgroundColor: colors.grey5 }}>
              <Ionicons name="play-circle" size={22} color={colors.foreground} />
            </View>
            <Text className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
              {hasStory ? 'View Story' : 'No Story Available'}
            </Text>
          </TouchableOpacity>
        </BottomSheetView>
      </BottomSheet>
    );
  }
);

export default StoryViewSheet;
