import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { View, Text, Image, FlatList } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '~/components/RenderBackdrop';
import { StoryViewer } from '~/types/story_type';

export interface ViewersBottomSheetHandle {
  present: (viewers: StoryViewer[]) => void;
  dismiss: () => void;
}

interface ViewersBottomSheetProps {}

const ViewersBottomSheet = forwardRef<ViewersBottomSheetHandle, ViewersBottomSheetProps>(
  (props, ref) => {
    const bottomSheetRef = useRef<BottomSheet>(null);
    const { colors, colorScheme } = useColorScheme();
    const isDark = colorScheme === 'dark';
    const [viewers, setViewers] = React.useState<StoryViewer[]>([]);

    const snapPoints = ['50%'];

    useImperativeHandle(ref, () => ({
      present: (viewers: StoryViewer[]) => {
        setViewers(viewers);
        bottomSheetRef.current?.expand();
      },
      dismiss: () => {
        bottomSheetRef.current?.close();
      },
    }));

    const formatTimeAgo = (dateString: string) => {
      const date = new Date(dateString);
      const now = new Date();
      const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
      if (diffInSeconds < 60) {
        return `${diffInSeconds}s ago`;
      } else if (diffInSeconds < 3600) {
        return `${Math.floor(diffInSeconds / 60)}m ago`;
      } else if (diffInSeconds < 86400) {
        return `${Math.floor(diffInSeconds / 3600)}h ago`;
      } else {
        return `${Math.floor(diffInSeconds / 86400)}d ago`;
      }
    };

    const renderViewerItem = ({ item }: { item: StoryViewer }) => (
      <View className="flex-row items-center py-3">
        <Image 
          source={{ uri: item.profilePhoto }} 
          className="h-12 w-12 rounded-full mr-4" 
          resizeMode="cover"
        />
        <View className="flex-1">
          <Text className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
            {item.name}
          </Text>
          <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            {formatTimeAgo(item.viewedAt)}
          </Text>
        </View>
      </View>
    );

    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        enablePanDownToClose
        backdropComponent={RenderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <BottomSheetView className="flex-1 px-4 pt-2">
          <Text
            className={`text-center font-medium text-lg mb-4 ${isDark ? 'text-white' : 'text-black'}`}>
            {viewers.length} {viewers.length === 1 ? 'View' : 'Views'}
          </Text>
          
          <FlatList
            data={viewers}
            renderItem={renderViewerItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={{ paddingBottom: 20 }}
            showsVerticalScrollIndicator={false}
          />
        </BottomSheetView>
      </BottomSheet>
    );
  }
);

export default ViewersBottomSheet;
