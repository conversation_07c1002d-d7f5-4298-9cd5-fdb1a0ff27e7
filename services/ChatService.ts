import * as SecureStore from 'expo-secure-store';
import axiosInstance from './config';

export class ChatService {
  static async createFirebaseUser(userDetails: {
    fullName: string;
    email: string;
    userId: string;
  }) {
    try {
      const formData = new FormData();
      formData.append('fullName', userDetails.fullName);
      formData.append('email', userDetails.email);
      formData.append('userId', userDetails.userId);
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/chat/v1/create-firebase-user', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async createPrivateChat(userOneId: string, userTwoId: string) {
    try {
      const formData = new FormData();
      formData.append('userOneId', userOneId);
      formData.append('userTwoId', userTwoId);
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/chat/v1/create-private-chat', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async sendMessage(
    imageFiles: File,
    chatId: string,
    senderId: string,
    receiverId: string,
    messageText: string
  ) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const formData = new FormData();

      if (imageFiles) {
        formData.append('messageUploads', {
          uri: imageFiles,
          name: chatId + 'chatImage.jpg',
          type: 'image/jpeg',
        } as any);
        formData.append('chatId', chatId);
        formData.append('senderId', senderId);
        formData.append('receiverId', receiverId);
        formData.append('messageText', messageText === undefined ? ' ' : messageText);
      } else {
        formData.append('chatId', chatId);
        formData.append('senderId', senderId);
        formData.append('receiverId', receiverId);
        formData.append('messageText', messageText === undefined ? ' ' : messageText);
      }

      const response = await axiosInstance.post('/chat/v1/send-message', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log(response.data);

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  // GROUP CHAT METHODS

  static async createGroupChat(groupDetails: {
    groupName: string;
    groupType: 'COMMUNITY' | 'EVENT';
    eventId?: string;
    communityId?: string;
    groupIcon?: string;
    members: string[]; // Array of user IDs
  }) {
    try {
      const formData = new FormData();
      formData.append('groupName', groupDetails.groupName);
      formData.append('groupType', groupDetails.groupType);
      if (groupDetails.eventId) {
        formData.append('eventId', groupDetails.eventId);
      }
      if (groupDetails.communityId) {
        formData.append('communityId', groupDetails.communityId);
      }
      if (groupDetails.groupIcon) {
        formData.append('groupIcon', groupDetails.groupIcon);
      }
      formData.append('members', JSON.stringify(groupDetails.members));

      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/chat/v1/create-group-chat', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async sendGroupMessage(
    imageFiles: string[],
    groupChatId: string,
    senderId: string,
    messageText: string
  ) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const formData = new FormData();

      if (imageFiles && imageFiles.length > 0) {
        formData.append('messageUploads', {
          uri: imageFiles[0],
          name: groupChatId + 'groupChatImage.jpg',
          type: 'image/jpeg',
        } as any);
        formData.append('groupChatId', groupChatId);
        formData.append('senderId', senderId);
        formData.append('messageText', messageText === undefined ? ' ' : messageText);
      } else {
        formData.append('groupChatId', groupChatId);
        formData.append('senderId', senderId);
        formData.append('messageText', messageText === undefined ? ' ' : messageText);
      }

      console.log('send messagesdddd,ddldlld', formData.getAll('messageUploads'));

      const response = await axiosInstance.post('/chat/v1/send-group-message', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async joinGroupChat(groupId: string) {
    try {
      const formData = new FormData();
      formData.append('groupId', groupId);

      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/chat/v1/join-group-chat', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async leaveGroupChat(groupId: string) {
    try {
      const formData = new FormData();
      formData.append('groupId', groupId);

      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/chat/v1/leave-group-chat', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async getGroupChatMembers(groupId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get(`/chat/v1/group-chat/${groupId}/members`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }
}
