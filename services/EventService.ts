import * as SecureStore from 'expo-secure-store';

import axiosInstance from './config';

export class EventService {
  static async createEventCategory(categoryName: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/event/v1/create-event-category', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        data: {
          categoryName,
        },
      });

      if (response.status !== 200) {
        throw new Error('Failed to create event category');
      }

      const data = response.data;
      return data;
    } catch (error) {
      throw error;
    }
  }

  static async getEventCategories() {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get('/event/v1/get-events-categories', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status !== 200) {
        throw new Error('Failed to fetch event categories');
      }

      const data = response.data;
      return data;
    } catch (error) {
      throw error;
    }
  }

  static async createEvent(eventData: FormData) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/event/v1/create-event', eventData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.status !== 200) {
        throw new Error('Failed to create event');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async getEvents(queryParams: any = {}) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get('/event/v1/get-events', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: queryParams,
      });

      if (response.status !== 200) {
        throw new Error('Failed to fetch events');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async registerToAttendEvent(eventId: string, ticketTypes: any[]) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.put(
        '/event/v1/register-attend-event',
        {
          eventId,
          ticketTypes,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status !== 200) {
        throw new Error('Failed to register for the event');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async getEventSuggestions(userId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get('/event/v1/events-suggestions', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: { userId },
      });

      if (response.status !== 200) {
        throw new Error('Failed to fetch event suggestions');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async saveEvent(userId: string, eventId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post(
        '/event/v1/save-event',
        {
          userId,
          eventId,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.status !== 200) {
        throw new Error('Failed to save event');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async getSavedEvents(userId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get('/event/v1/get-saved-events', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: { userId },
      });

      if (response.status !== 200) {
        throw new Error('Failed to fetch saved events');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async registerForEvent(payload: {
    eventId: string;
    ticketType: string;
    price: number;
    quantity: number;
  }) {
    try {
      console.log(payload);
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/event/v1/register-attend-event', payload, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.status !== 200) {
        throw new Error('Failed to register for event');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async promoteEvent(payload: { eventId: string; promotionalPackage: string }) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/event/v1/promote-event', payload, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.status !== 200) {
        throw new Error('Failed to promote event');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }
}
