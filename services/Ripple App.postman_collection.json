{"info": {"_postman_id": "b7d1c2ea-c6a6-4079-ba3d-4be1a70346f1", "name": "Ripple App", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "44950161", "_collection_link": "https://valentine-sean-dev.postman.co/workspace/RESEARCH-LAB~737029e1-bc22-44e3-b2af-9353161de03e/collection/15317111-b7d1c2ea-c6a6-4079-ba3d-4be1a70346f1?action=share&source=collection_link&creator=44950161"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "initiate_registration", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"seanchanenge<PERSON>@gmail.com\",\r\n    \"phoneNumber\": \"718787946\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/auth/v1/initiate-registration", "host": ["localhost"], "port": "5025", "path": ["auth", "v1", "initiate-registration"]}}, "response": []}, {"name": "send_otp", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"seanchanenge<PERSON>@gmail.com\",\r\n    \"phoneNumber\": \"718787946\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/auth/v1/send-otp", "host": ["localhost"], "port": "5025", "path": ["auth", "v1", "send-otp"]}}, "response": []}, {"name": "verify_email", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"verificationCode\": \"1092\",\r\n    \"userId\": \"2606330c8e9349a9abfe30f6c3c11f30\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/auth/v1/verify-email", "host": ["localhost"], "port": "5025", "path": ["auth", "v1", "verify-email"]}}, "response": []}, {"name": "verify_phone_number", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"verificationCode\": \"0000\",\r\n    \"userId\": \"XXXXXX\"\r\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "register_user", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"fullName\": \"<PERSON>\",\r\n    \"email\": \"seanchane<PERSON><PERSON>@gmail.com\",\r\n    \"phoneNumber\": \"718787946\",\r\n    \"password\": \"123456\",\r\n    \"verificationCode\": \"1092\",\r\n    \"userId\": \"2606330c8e9349a9abfe30f6c3c11f30\",\r\n    \"termsAccepted\": true\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "**************:5025/auth/v1/register-user", "host": ["38", "242", "235", "101"], "port": "5025", "path": ["auth", "v1", "register-user"]}}, "response": []}, {"name": "login", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"seanchanenge<PERSON>@gmail.com\",\r\n    \"phoneNumber\": \"718787946\",\r\n    \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "**************:5025/auth/v1/login", "host": ["38", "242", "235", "101"], "port": "5025", "path": ["auth", "v1", "login"]}}, "response": [{"name": "login", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"seanchanenge<PERSON>@gmail.com\",\r\n    \"phoneNumber\": \"718787946\",\r\n    \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "**************:5025/auth/v1/login", "host": ["38", "242", "235", "101"], "port": "5025", "path": ["auth", "v1", "login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 14 May 2025 19:49:02 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Login successful\",\n    \"body\": {\n        \"accessToken\": \"eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.sMEVneqQMjps5lGCJEMSXvbzb8x2j-99Xim9Zp5dzaaWYm8LquI5e_-g8sYgdEMeCuj4MjztfTlxRWJk7h2-Qw\",\n        \"user\": {\n            \"id\": \"68036852f01b4537621ac95d\",\n            \"createdAt\": null,\n            \"updatedAt\": null,\n            \"active\": true,\n            \"version\": 14,\n            \"createdBy\": null,\n            \"updatedBy\": null,\n            \"deletedBy\": null,\n            \"fullName\": \"Valentine Chanengeta\",\n            \"phoneNumber\": \"*********\",\n            \"email\": \"<EMAIL>\",\n            \"username\": \"<EMAIL>\",\n            \"role\": {\n                \"id\": \"67fe2cb0468f0740e5767489\",\n                \"createdAt\": null,\n                \"updatedAt\": null,\n                \"active\": true,\n                \"version\": 1,\n                \"createdBy\": null,\n                \"updatedBy\": null,\n                \"deletedBy\": null,\n                \"roleName\": \"USER\"\n            },\n            \"password\": \"$2a$10$kr04z2E8TI/7UmItA6DLouNsdV57ZLB7iZEXZbf/Nz.JyHyvEM9.2\",\n            \"verified\": true,\n            \"emailVerified\": true,\n            \"phoneNumberVerified\": false,\n            \"refreshToken\": \"88b592ad7f334b6d99ea88b17b6bf5f7\",\n            \"gender\": \"MALE\",\n            \"interests\": [\n                \"Technology\",\n                \"Music\",\n                \"Travel\",\n                \"Art\",\n                \"Fitness\",\n                \"Gaming\"\n            ],\n            \"eventPreferences\": [\n                \"Leisure\",\n                \"Entertainment\",\n                \"Education\",\n                \"Business\"\n            ],\n            \"bio\": \"One, Two, Three....\",\n            \"profileImage\": null,\n            \"imageUploads\": null,\n            \"stats\": null,\n            \"upFor\": \"Gaming...\",\n            \"setupComplete\": true\n        },\n        \"refreshToken\": \"88b592ad7f334b6d99ea88b17b6bf5f7\"\n    }\n}"}]}, {"name": "refresh_token", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"refreshToken\": \"669a3c4a81fe4e5ea26cd1446b740f9d\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/auth/v1/refresh-token", "host": ["localhost"], "port": "5025", "path": ["auth", "v1", "refresh-token"]}}, "response": []}, {"name": "forgot_password", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"newPassword\": \"0000\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/auth/v1/forgot-password", "host": ["localhost"], "port": "5025", "path": ["auth", "v1", "forgot-password"]}}, "response": []}, {"name": "reset_password", "request": {"method": "POST", "header": []}, "response": []}, {"name": "change_password", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.n8x90E0ylcRbAK6Ab9hGG-6r4acD7sltmqJwXaNgIHbYFoeaJBsju41mzyp_704JnlDFRV8oVYVV5OBz82cCdg", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"oldPassword\": \"0000\",\r\n    \"newPassword\": \"1111\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/auth/v1/change-password", "host": ["localhost"], "port": "5025", "path": ["auth", "v1", "change-password"]}}, "response": []}, {"name": "logout", "request": {"method": "POST", "header": []}, "response": []}]}, {"name": "Role Management", "item": [{"name": "create_role", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"roleName\": \"User\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/role/v1/create-role", "host": ["localhost"], "port": "5025", "path": ["role", "v1", "create-role"]}}, "response": []}]}, {"name": "User Management", "item": [{"name": "create_user", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"phoneNumber\": \"*********\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"firstName\": \"<PERSON>\",\r\n    \"lastName\": \"<PERSON><PERSON><PERSON>\",\r\n    \"roleId\": \"67ef09eca2000102febf2e3e\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/user/v1/create-user", "host": ["localhost"], "port": "5025", "path": ["user", "v1", "create-user"]}}, "response": []}, {"name": "profile_setup", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.s76bOcO3PeGgdavP3eWEvGkM_AeRZIFWNqNcqflFt9fQpnGiipJ8ApVmxn9lJLo3-A0DUTy_UjPoSrpWLW9E6A", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"phoneNumber\": \"*********\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"gender\": \"male\",\r\n    \"interests\": [\"Technology\", \"Music\", \"Travel\", \"Art\"],\r\n    \"eventPreferences\": [\"Leisure\", \"Entertainment\", \"Education\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/user/v1/profile-setup", "host": ["localhost"], "port": "5025", "path": ["user", "v1", "profile-setup"]}}, "response": []}, {"name": "update_profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.s76bOcO3PeGgdavP3eWEvGkM_AeRZIFWNqNcqflFt9fQpnGiipJ8ApVmxn9lJLo3-A0DUTy_UjPoSrpWLW9E6A", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userId\": \"XXXXX\",\r\n    \"phoneNumber\": \"*********\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"fullName\": \"<PERSON> Chanengeta\",\r\n    \"gender\": \"male\",\r\n    \"interests\": [\"Technology\", \"Music\", \"Travel\", \"Art\", \"Fitness\", \"Gaming\"],\r\n    \"eventPreferences\": [\"Leisure\", \"Entertainment\", \"Education\", \"Business\"],\r\n    \"upFor\": \"Gaming...\",\r\n    \"bio\": \"One, Two, Three....\",\r\n    \"visibility\": \"private\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/user/v1/update-profile", "host": ["localhost"], "port": "5025", "path": ["user", "v1", "update-profile"]}}, "response": []}, {"name": "get_my_profile", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.s76bOcO3PeGgdavP3eWEvGkM_AeRZIFWNqNcqflFt9fQpnGiipJ8ApVmxn9lJLo3-A0DUTy_UjPoSrpWLW9E6A", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"phoneNumber\": \"*********\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"gender\": \"male\",\r\n    \"interests\": [\"Technology\", \"Music\", \"Travel\", \"Art\"],\r\n    \"eventPreferences\": [\"Leisure\", \"Entertainment\", \"Education\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/user/v1/get-my-profile", "host": ["localhost"], "port": "5025", "path": ["user", "v1", "get-my-profile"]}}, "response": []}, {"name": "get_other_user", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.s76bOcO3PeGgdavP3eWEvGkM_AeRZIFWNqNcqflFt9fQpnGiipJ8ApVmxn9lJLo3-A0DUTy_UjPoSrpWLW9E6A", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"phoneNumber\": \"*********\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"gender\": \"male\",\r\n    \"interests\": [\"Technology\", \"Music\", \"Travel\", \"Art\"],\r\n    \"eventPreferences\": [\"Leisure\", \"Entertainment\", \"Education\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/user/v1/get-other-user?email=<EMAIL>", "host": ["localhost"], "port": "5025", "path": ["user", "v1", "get-other-user"], "query": [{"key": "email", "value": "<EMAIL>"}]}}, "response": []}]}, {"name": "Location", "item": [{"name": "calculate_distance", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0dGiMi8EeISY_PHM5vApA0PjCrDWsvYM1JfbDS0hUoBBgEQRdvoRLmELvIgM0Zo2SUUkdwx-KVjo2pb7DmJEpQ", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "localhost:5025/location/v1/calculate-distance", "host": ["localhost"], "port": "5025", "path": ["location", "v1", "calculate-distance"]}}, "response": []}, {"name": "find_nearby_places", "request": {"method": "POST", "header": [], "url": {"raw": "localhost:5025/location/v1/find-nearby-places", "host": ["localhost"], "port": "5025", "path": ["location", "v1", "find-nearby-places"]}}, "response": []}]}, {"name": "Payment", "item": [{"name": "test_paynow", "request": {"method": "POST", "header": [], "url": {"raw": "localhost:5025/payment/v1/test-paynow", "host": ["localhost"], "port": "5025", "path": ["payment", "v1", "test-paynow"]}}, "response": []}]}, {"name": "File Management", "item": [{"name": "upload_image", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.s76bOcO3PeGgdavP3eWEvGkM_AeRZIFWNqNcqflFt9fQpnGiipJ8ApVmxn9lJLo3-A0DUTy_UjPoSrpWLW9E6A", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "imageFile", "type": "file", "src": "/C:/Users/<USER>/Downloads/uefa.jpeg"}, {"key": "email", "value": "<EMAIL>", "type": "text"}]}, "url": {"raw": "localhost:5025/file/v1/upload-image", "host": ["localhost"], "port": "5025", "path": ["file", "v1", "upload-image"]}}, "response": []}, {"name": "upload_many_images", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.JyWwKLeK4OqLRNQHsAopVpUevS45qjSD98SFym6JZqc4fIDxuEGK8U-BYRhdjgPrwAO0SI6-n8Q5-HHDpieGpQ", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "imageFiles", "type": "file", "src": ["/C:/Users/<USER>/Downloads/GowhVJ2WcAEID8N.jpeg", "/C:/Users/<USER>/Downloads/GolJhCaX0AE1RSi.jpeg"]}, {"key": "email", "value": "<EMAIL>", "type": "text"}]}, "url": {"raw": "localhost:5025/file/v1/upload-many-images", "host": ["localhost"], "port": "5025", "path": ["file", "v1", "upload-many-images"]}}, "response": []}, {"name": "get_image", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.s76bOcO3PeGgdavP3eWEvGkM_AeRZIFWNqNcqflFt9fQpnGiipJ8ApVmxn9lJLo3-A0DUTy_UjPoSrpWLW9E6A", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "imageFile", "type": "file", "src": ["/C:/Users/<USER>/Downloads/WhatsApp Image 2025-04-17 at 22.51.14.jpeg", "/C:/Users/<USER>/Downloads/WhatsApp Image 2025-04-18 at 23.32.37.jpeg"], "disabled": true}, {"key": "fileUploadId", "value": "6804b3bb207fc91c8349531f", "type": "text"}]}, "url": {"raw": "localhost:5025/file/v1/get-image", "host": ["localhost"], "port": "5025", "path": ["file", "v1", "get-image"]}}, "response": []}]}, {"name": "Event Management", "item": [{"name": "create_event_category", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.S5oUa9PaXv9v7q-jGmBmeuc9dThH0zUv2IQiJh4Dn4b8_XPvFLYjai2rqAJJpYj6NNjCUtdNGNOwWkU7CcSwtQ", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"categoryName\": \"Entertainment\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/event/v1/create-event-category", "host": ["localhost"], "port": "5025", "path": ["event", "v1", "create-event-category"]}}, "response": []}, {"name": "get_events_categories", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.S5oUa9PaXv9v7q-jGmBmeuc9dThH0zUv2IQiJh4Dn4b8_XPvFLYjai2rqAJJpYj6NNjCUtdNGNOwWkU7CcSwtQ", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "localhost:5025/event/v1/get-events-categories", "host": ["localhost"], "port": "5025", "path": ["event", "v1", "get-events-categories"]}}, "response": []}, {"name": "create_event", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.EXeiko2TNA8r_jQEGALPl3I-jN3d6IeFANTEziq9TFKU07JlbifJiAS9OkpMV0tlV7vHBMpGD16LACkrPVsO5g", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "coverImages", "type": "file", "src": ["/C:/Users/<USER>/Downloads/GokOCltX0AAf4Nx.jpeg", "/C:/Users/<USER>/Downloads/GosCK0WWMAAHaeq.jpeg"]}, {"key": "coverImages", "type": "file", "src": [], "disabled": true}, {"key": "ticketSetup[levels]", "value": "", "type": "text", "disabled": true}, {"key": "eventCategoryId", "value": "6807894bf8e5a224aae680a5", "type": "text"}, {"key": "title", "value": "Tech Meetup 2025", "type": "text"}, {"key": "description", "value": "Monthly tech meetup for developers and tech enthusiasts", "type": "text"}, {"key": "location", "value": "Innovation Hub, Harare", "type": "text"}, {"key": "locationData[coordinates]", "value": "31.0701", "type": "text", "disabled": true}, {"key": "startDateTime", "value": "2025-04-13T10:00:00", "type": "text"}, {"key": "endDateTime", "value": "2025-04-13T17:00:00", "type": "text"}, {"key": "isPaid", "value": "true", "type": "text"}, {"key": "ticketSetup[hasLevels]", "value": "true", "type": "text", "disabled": true}, {"key": "owners", "value": "Panash<PERSON>", "type": "text"}, {"key": "currency", "value": "USD", "type": "text"}, {"key": "visibility", "value": "PRIVATE", "type": "text"}, {"key": "locationData", "value": "{\"coordinates\": [31.0701, -17.8127], \"name\": \"Innovation Hub\", \"address\": \"45 Tech Street, Harare\"}", "type": "text"}, {"key": "locationData[name]", "value": "Innovation Hub", "type": "text", "disabled": true}, {"key": "locationData[address]", "value": "45 Tech Street, Harare", "type": "text", "disabled": true}, {"key": "ticketSetup", "value": "{\"hasLevels\": true, \"totalTickets\": null,\"price\": null,\"levels\": [{\"type\": \"Basic\",\"quantity\": 50,\"price\": 5},{\"type\": \"VIP\",\"quantity\": 10,\"price\": 15}]}", "type": "text"}, {"key": "ticketSetup[price]", "value": "", "type": "text", "disabled": true}, {"key": "ticketSetup[levels][type]", "value": "Basic", "type": "text", "disabled": true}, {"key": "ticketSetup[levels][quantity]", "value": "50", "type": "text", "disabled": true}, {"key": "ticketSetup[levels][price]", "value": "5", "type": "text", "disabled": true}, {"key": "ticketSetup[levels][type]", "value": "VIP", "type": "text", "disabled": true}, {"key": "ticketSetup[levels][quantity]", "value": "10", "type": "text", "disabled": true}, {"key": "ticketSetup[levels][price]", "value": "15", "type": "text", "disabled": true}]}, "url": {"raw": "localhost:5025/event/v1/create-event", "host": ["localhost"], "port": "5025", "path": ["event", "v1", "create-event"]}}, "response": []}, {"name": "CREATE_EVENT_JSON", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.EXeiko2TNA8r_jQEGALPl3I-jN3d6IeFANTEziq9TFKU07JlbifJiAS9OkpMV0tlV7vHBMpGD16LACkrPVsO5g", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ticketSetup\": {\r\n        \"hasLevels\": true,\r\n        \"totalTickets\": null,\r\n        \"price\": null,\r\n        \"levels\": [\r\n            {\r\n                \"type\": \"Basic\",\r\n                \"quantity\": 50,\r\n                \"price\": 5\r\n            },\r\n\r\n            {\r\n                \"type\": \"VIP\",\r\n                \"quantity\": 10,\r\n                \"price\": 15\r\n            }\r\n        ]\r\n    },\r\n\r\n    \"locationData\": {\"coordinates\": [31.0701, -17.8127], \"name\": \"Innovation Hub\", \"address\": \"45 Tech Street, Harare\"}\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/event/v1/create-event", "host": ["localhost"], "port": "5025", "path": ["event", "v1", "create-event"]}}, "response": []}, {"name": "get_events", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzZWFuY2hhbmVuZ2V0YUBnbWFpbC5jb20iLCJqdGkiOiI2ODAzNjg1MmYwMWI0NTM3NjIxYWM5NWQiLCJmdWxsTmFtZSI6IlZhbGVudGluZSBDaGFuZW5nZXRhIiwicGhvbmVOdW1iZXIiOiI3NzIyNzk3NTAiLCJlbWFpbCI6InNlYW5jaGFuZW5nZXRhQGdtYWlsLmNvbSIsInJvbGVJZCI6IjY3ZmUyY2IwNDY4ZjA3NDBlNTc2NzQ4OSIsInJvbGUiOiJVU0VSIiwiaWF0IjoxNzQ2NDQ0ODI3LCJleHAiOjE3NDkwMzY4Mjd9.Pmla2P9824IFwYwm5D5KBlWsr4M7wxjX-X3FaoAPLdxaEC1WDfILrmoNQkUreX8gP781TTj9PslU5iSzhB-o1A", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "localhost:5025/event/v1/get-events", "host": ["localhost"], "port": "5025", "path": ["event", "v1", "get-events"], "query": [{"key": "visibility", "value": "PUBLIC", "disabled": true}, {"key": "eventCategoryId", "value": "6807894bf8e5a224aae680a5", "disabled": true}, {"key": "eventId", "value": "68176d8562d0b05d3ea55801", "disabled": true}]}}, "response": []}, {"name": "register_attend_event", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzZWFuY2hhbmVuZ2V0YUBnbWFpbC5jb20iLCJqdGkiOiI2ODAzNjg1MmYwMWI0NTM3NjIxYWM5NWQiLCJmdWxsTmFtZSI6IlZhbGVudGluZSBDaGFuZW5nZXRhIiwicGhvbmVOdW1iZXIiOiI3NzIyNzk3NTAiLCJlbWFpbCI6InNlYW5jaGFuZW5nZXRhQGdtYWlsLmNvbSIsInJvbGVJZCI6IjY3ZmUyY2IwNDY4ZjA3NDBlNTc2NzQ4OSIsInJvbGUiOiJVU0VSIiwiaWF0IjoxNzQ2ODIwNjg0LCJleHAiOjE3NDk0MTI2ODR9.6-oDMKNXlAz8zw5FF5lbNri1VIJOJz1W4YRQwN0WKttq8VyM0tOX3KKg-A9IwQdXMTYL8B-Lu0rFP55DTmLMlA", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"eventId\": \"68176d8562d0b05d3ea55801\",\r\n    \"ticketTypes\": [\r\n        {\r\n            \"ticketType\": \"Free\",\r\n            \"price\": 0.00,\r\n            \"quantity\": 10\r\n        },\r\n        {\r\n            \"ticketType\": \"Basic\",\r\n            \"price\": 5.00,\r\n            \"quantity\": 3\r\n        },\r\n        {\r\n            \"ticketType\": \"Premium\",\r\n            \"price\": 5.00,\r\n            \"quantity\": 3\r\n        },\r\n        {\r\n            \"ticketType\": \"Enterprise\",\r\n            \"price\": 5.00,\r\n            \"quantity\": 3\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/event/v1/register-attend-event", "host": ["localhost"], "port": "5025", "path": ["event", "v1", "register-attend-event"]}}, "response": []}, {"name": "events_suggestions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzZWFuY2hhbmVuZ2V0YUBnbWFpbC5jb20iLCJqdGkiOiI2ODAzNjg1MmYwMWI0NTM3NjIxYWM5NWQiLCJmdWxsTmFtZSI6IlZhbGVudGluZSBDaGFuZW5nZXRhIiwicGhvbmVOdW1iZXIiOiI3NzIyNzk3NTAiLCJlbWFpbCI6InNlYW5jaGFuZW5nZXRhQGdtYWlsLmNvbSIsInJvbGVJZCI6IjY3ZmUyY2IwNDY4ZjA3NDBlNTc2NzQ4OSIsInJvbGUiOiJVU0VSIiwiaWF0IjoxNzQ3ODM0MjI1LCJleHAiOjE3NTA0MjYyMjV9.9_JWQFvgNERpPeXSWwrSsQ1s7VcJqX_-5NniAGcC7Xx-SmanhO9ztHS8pOVUeeNPqHgMocqLVF1Wt9eW-Ir4Kg", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "localhost:5025/event/v1/events-suggestions?userId=68036852f01b4537621ac95d", "host": ["localhost"], "port": "5025", "path": ["event", "v1", "events-suggestions"], "query": [{"key": "userId", "value": "68036852f01b4537621ac95d"}]}}, "response": []}]}, {"name": "Friend Management", "item": [{"name": "send_friend_request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.sMEVneqQMjps5lGCJEMSXvbzb8x2j-99Xim9Zp5dzaaWYm8LquI5e_-g8sYgdEMeCuj4MjztfTlxRWJk7h2-Qw", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"requesterId\": \"68036852f01b4537621ac95d\",\r\n    \"requesteeId\": \"68036852f01b4537621ac95e\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/friend/v1/send-friend-request", "host": ["localhost"], "port": "5025", "path": ["friend", "v1", "send-friend-request"]}}, "response": []}, {"name": "accept_friend_request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzZWFuY2hhbmVuZ2V0YUBnbWFpbC5jb20iLCJqdGkiOiI2ODAzNjg1MmYwMWI0NTM3NjIxYWM5NWQiLCJmdWxsTmFtZSI6IlZhbGVudGluZSBDaGFuZW5nZXRhIiwicGhvbmVOdW1iZXIiOiI3NzIyNzk3NTAiLCJlbWFpbCI6InNlYW5jaGFuZW5nZXRhQGdtYWlsLmNvbSIsInJvbGVJZCI6IjY3ZmUyY2IwNDY4ZjA3NDBlNTc2NzQ4OSIsInJvbGUiOiJVU0VSIiwiaWF0IjoxNzQ3NjQ5NTc5LCJleHAiOjE3NTAyNDE1Nzl9.LMLo8ZnySyfueupzR9-tBKZZkWh6B0tK46_-3gHm-MBK8yF2B9XDKwCBWoihHDgSnd2sFIQOuyiPwG44RuhK-g", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"friendRequestId\": \"682b05dce8dde2083d2780e4\",\r\n    \"status\": \"REJECTED\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5025/friend/v1/accept-friend-request", "host": ["localhost"], "port": "5025", "path": ["friend", "v1", "accept-friend-request"]}}, "response": []}, {"name": "get_sent_friend_requests", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzZWFuY2hhbmVuZ2V0YUBnbWFpbC5jb20iLCJqdGkiOiI2ODAzNjg1MmYwMWI0NTM3NjIxYWM5NWQiLCJmdWxsTmFtZSI6IlZhbGVudGluZSBDaGFuZW5nZXRhIiwicGhvbmVOdW1iZXIiOiI3NzIyNzk3NTAiLCJlbWFpbCI6InNlYW5jaGFuZW5nZXRhQGdtYWlsLmNvbSIsInJvbGVJZCI6IjY3ZmUyY2IwNDY4ZjA3NDBlNTc2NzQ4OSIsInJvbGUiOiJVU0VSIiwiaWF0IjoxNzQ3NjQ5NTc5LCJleHAiOjE3NTAyNDE1Nzl9.LMLo8ZnySyfueupzR9-tBKZZkWh6B0tK46_-3gHm-MBK8yF2B9XDKwCBWoihHDgSnd2sFIQOuyiPwG44RuhK-g", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "localhost:5025/friend/v1/get-sent-friend-requests?userId=68036852f01b4537621ac95d", "host": ["localhost"], "port": "5025", "path": ["friend", "v1", "get-sent-friend-requests"], "query": [{"key": "userId", "value": "68036852f01b4537621ac95d"}, {"key": "status", "value": "PENDING", "disabled": true}]}}, "response": []}, {"name": "get_received_friend_requests", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzZWFuY2hhbmVuZ2V0YUBnbWFpbC5jb20iLCJqdGkiOiI2ODAzNjg1MmYwMWI0NTM3NjIxYWM5NWQiLCJmdWxsTmFtZSI6IlZhbGVudGluZSBDaGFuZW5nZXRhIiwicGhvbmVOdW1iZXIiOiI3NzIyNzk3NTAiLCJlbWFpbCI6InNlYW5jaGFuZW5nZXRhQGdtYWlsLmNvbSIsInJvbGVJZCI6IjY3ZmUyY2IwNDY4ZjA3NDBlNTc2NzQ4OSIsInJvbGUiOiJVU0VSIiwiaWF0IjoxNzQ3NjQ5NTc5LCJleHAiOjE3NTAyNDE1Nzl9.LMLo8ZnySyfueupzR9-tBKZZkWh6B0tK46_-3gHm-MBK8yF2B9XDKwCBWoihHDgSnd2sFIQOuyiPwG44RuhK-g", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "localhost:5025/friend/v1/get-received-friend-requests?userId=68036852f01b4537621ac95f", "host": ["localhost"], "port": "5025", "path": ["friend", "v1", "get-received-friend-requests"], "query": [{"key": "userId", "value": "68036852f01b4537621ac95f"}, {"key": "status", "value": "PENDING", "disabled": true}]}}, "response": []}, {"name": "get_friends", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzZWFuY2hhbmVuZ2V0YUBnbWFpbC5jb20iLCJqdGkiOiI2ODAzNjg1MmYwMWI0NTM3NjIxYWM5NWQiLCJmdWxsTmFtZSI6IlZhbGVudGluZSBDaGFuZW5nZXRhIiwicGhvbmVOdW1iZXIiOiI3NzIyNzk3NTAiLCJlbWFpbCI6InNlYW5jaGFuZW5nZXRhQGdtYWlsLmNvbSIsInJvbGVJZCI6IjY3ZmUyY2IwNDY4ZjA3NDBlNTc2NzQ4OSIsInJvbGUiOiJVU0VSIiwiaWF0IjoxNzQ3NjQ5NTc5LCJleHAiOjE3NTAyNDE1Nzl9.LMLo8ZnySyfueupzR9-tBKZZkWh6B0tK46_-3gHm-MBK8yF2B9XDKwCBWoihHDgSnd2sFIQOuyiPwG44RuhK-g", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "localhost:5025/friend/v1/get-friends?userId=68036852f01b4537621ac95d", "host": ["localhost"], "port": "5025", "path": ["friend", "v1", "get-friends"], "query": [{"key": "userId", "value": "68036852f01b4537621ac95d"}]}}, "response": []}]}]}