import * as SecureStore from 'expo-secure-store';
import axiosInstance from './config';

export class FriendService {
  static async sendFriendRequest(requesterId: string, requesteeId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post(
        '/friend/v1/send-friend-request',
        {
          requesterId,
          requesteeId,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status !== 200) {
        throw new Error('Failed to send request');
      }
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async acceptFriendRequest(friendRequestId: string, status: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post(
        '/friend/v1/accept-friend-request',
        {
          friendRequestId,
          status,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status !== 200) {
        throw new Error('Failed to accept/reject request');
      }
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async getFriends(userId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get('/friend/v1/get-friends?userId=' + userId, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status !== 200) {
        throw new Error('Failed to fetch friends');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async getFriendRequests(userId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get(
        '/friend/v1/get-received-friend-requests?userId=' + userId,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status !== 200) {
        throw new Error('Failed to fetch friend requests');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async getSentFriendRequests(userId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get(
        '/friend/v1/get-sent-friend-requests?userId=' + userId,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status !== 200) {
        throw new Error('Failed to fetch friend requests');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }
}
