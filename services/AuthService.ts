import * as SecureStore from 'expo-secure-store';
import axiosInstance from './config';
import { AuthStore, UserStore } from '~/store/store';
import { UserService } from './UserService';
import { FirebaseService } from './FirebaseService';

export class AuthService {
  static async initiateRegistration(email: string, phoneNumber: string) {
    try {
      const response = await axiosInstance.post('/auth/v1/initiate-registration', {
        email,
        phoneNumber,
      });

      const data = response.data;
      if (data.success) {
        (AuthStore.getState() as { InitialRegistration: (data) => void }).InitialRegistration(
          data.body
        );
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async login(email: string, password: string) {
    try {
      const response = await axiosInstance.post('/auth/v1/login', {
        email,
        password,
        location: [0, 0],
      });

      const data = response.data;

      if (data.success) {
        await SecureStore.setItemAsync('accessToken', data.body.accessToken);
        await SecureStore.setItemAsync('refreshToken', data.body.refreshToken);
        await SecureStore.setItemAsync('userData', JSON.stringify(data.body));
        const profileData = await UserService.getMyProfile();
        console.log(profileData.body.profilePicture);
        (UserStore.getState() as { setUser: (data) => void }).setUser(profileData.body);
        (AuthStore.getState() as { login: () => void }).login();

        // Ensure user exists in Firebase for chat functionality
        try {
          await FirebaseService.ensureFirebaseUser(
            profileData.body.id,
            profileData.body.fullName,
            profileData.body.email
          );
        } catch (firebaseError) {
          console.error('Error creating Firebase user:', firebaseError);
          // Don't fail login if Firebase user creation fails
        }

        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async logout() {
    try {
      await SecureStore.deleteItemAsync('accessToken');
      (AuthStore.getState() as { logout: () => void }).logout();
    } catch (error) {
      throw error;
    }
  }

  static async verifyEmail(verificationCode: string, userId: string) {
    try {
      const response = await axiosInstance.post('/auth/v1/verify-email', {
        verificationCode,
        userId,
      });
      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async sendOtp(email: string) {
    try {
      const response = await axiosInstance.post('/auth/v1/send-otp', {
        email,
      });
      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async refreshToken(refreshToken: string) {
    try {
      const response = await axiosInstance.post('/auth/v1/refresh-token', {
        refreshToken,
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async forgotPassword(email: string, newPassword: string) {
    try {
      const response = await axiosInstance.post('/auth/v1/forgot-password', {
        email,
        newPassword,
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async changePassword(email: string, oldPassword: string, newPassword: string) {
    try {
      const response = await axiosInstance.post('/auth/v1/change-password', {
        email,
        oldPassword,
        newPassword,
      });
      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }
}
