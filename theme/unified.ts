import { Platform } from 'react-native';

// Define the theme structure
export type ThemeColors = {
  background: string;
  card: string;
  primary: string;
  accent: string;
  text: string;
  textSecondary: string;
  border: string;
  error: string;
  success: string;
  warning: string;
  info: string;
};

export type Theme = {
  dark: boolean;
  colors: ThemeColors;
};

// Define the light theme
const lightTheme: Theme = {
  dark: false,
  colors: {
    background: '#D0C1E1', // Lightest shade from tailwind config
    card: '#9182C3', // Light card background from tailwind config
    primary: '#5A4FCF', // Primary buttons, links from tailwind config
    accent: '#2E2AA8', // Secondary actions, highlights from tailwind config
    text: '#1A176B', // Main text color from tailwind config
    textSecondary: '#4A4A4A',
    border: Platform.OS === 'ios' ? 'rgb(230, 230, 235)' : 'rgb(215, 217, 228)',
    error: Platform.OS === 'ios' ? 'rgb(255, 56, 43)' : 'rgb(186, 26, 26)',
    success: '#4CAF50',
    warning: '#FF9800',
    info: '#2196F3',
  },
};

// Define the dark theme
const darkTheme: Theme = {
  dark: true,
  colors: {
    background: '#1A176B', // Darkest shade from tailwind config
    card: '#2E2AA8', // Darker card background from tailwind config
    primary: '#5A4FCF', // Primary buttons, links from tailwind config
    accent: '#9182C3', // Secondary actions, highlights from tailwind config
    text: '#FFFFFF', // Main text color from tailwind config
    textSecondary: '#CCCCCC',
    border: Platform.OS === 'ios' ? 'rgb(40, 40, 42)' : 'rgb(39, 42, 50)',
    error: Platform.OS === 'ios' ? 'rgb(254, 67, 54)' : 'rgb(147, 0, 10)',
    success: '#66BB6A',
    warning: '#FFA726',
    info: '#42A5F5',
  },
};

// Font families
export const fontFamilies = {
  sans: 'sans',
  medium: 'medium',
  bold: 'bold',
};

// Font sizes with line heights
export const fontSizes = {
  xs: {
    fontSize: 12,
    lineHeight: 16,
  },
  sm: {
    fontSize: 14,
    lineHeight: 20,
  },
  base: {
    fontSize: 16,
    lineHeight: 24,
  },
  lg: {
    fontSize: 18,
    lineHeight: 28,
  },
  xl: {
    fontSize: 20,
    lineHeight: 28,
  },
  '2xl': {
    fontSize: 24,
    lineHeight: 32,
  },
};

export const UNIFIED_THEMES = {
  light: lightTheme,
  dark: darkTheme,
};
